# Database Dump Documentation

## PostgreSQL Version Information
- **PostgreSQL Version**: 16.9 (Ubuntu 16.9-0ubuntu0.24.04.1)
- **Environment**: Ubuntu 24.04.1

## Database Dump Files

### db1.dump
- **File**: db1.dump
- **Size**: 699,123 bytes (~683 KB)
- **Creation Date**: July 11, 2024 at 12:38
- **Database**: vbs_allocation_caps
- **Format**: Custom format with compression level 9
- **Created By**: pg_dump utility

### db.dump
- **File**: db.dump
- **Size**: 751,808 bytes (~734 KB)
- **Creation Date**: June 10, 2024 at 14:14
- **Database**: vbs_allocation_caps
- **Format**: Custom format with compression level 9
- **Created By**: pg_dump utility

## Backup Information
- **Backup Format**: PostgreSQL custom format (--format=custom)
- **Compression**: Level 9 (maximum compression)
- **Database Host**: localhost
- **Database User**: postgres

## Security Notes
- ✅ Directory ownership has been corrected (build/ directory now owned by piyushm:piyushm)
- ✅ Plaintext passwords have been removed from shell history
- ✅ Current session history has been cleared

## Usage Instructions
To restore from these dumps:
```bash
# Set password securely
export PGPASSWORD="your_password_here"

# Restore from db1.dump (most recent)
pg_restore -U postgres -d target_database_name db1.dump

# Or restore from db.dump
pg_restore -U postgres -d target_database_name db.dump
```

---
*Generated on: $(date)*
*PostgreSQL Version: 16.9*
*System: Ubuntu 24.04.1*
