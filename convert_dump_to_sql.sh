#!/bin/bash

# Script to convert PostgreSQL custom format dump to plain SQL format
# This makes it easier to debug and see the actual data

DUMP_FILE=${1:-"db1.dump"}
OUTPUT_FILE=${2:-"db1_converted.sql"}

echo "=== Converting PostgreSQL Custom Format Dump to SQL ==="
echo "Input file: $DUMP_FILE"
echo "Output file: $OUTPUT_FILE"
echo ""

# Check if dump file exists
if [ ! -f "$DUMP_FILE" ]; then
    echo "Error: Dump file '$DUMP_FILE' not found!"
    exit 1
fi

echo "Converting dump to SQL format..."
pg_restore --verbose --no-acl --no-owner --schema-only -f "${OUTPUT_FILE%.sql}_schema.sql" "$DUMP_FILE"
pg_restore --verbose --no-acl --no-owner --data-only -f "${OUTPUT_FILE%.sql}_data.sql" "$DUMP_FILE"

# Create combined file
echo "-- Combined SQL dump converted from custom format" > "$OUTPUT_FILE"
echo "-- Schema section" >> "$OUTPUT_FILE"
cat "${OUTPUT_FILE%.sql}_schema.sql" >> "$OUTPUT_FILE"
echo "" >> "$OUTPUT_FILE"
echo "-- Data section" >> "$OUTPUT_FILE"
cat "${OUTPUT_FILE%.sql}_data.sql" >> "$OUTPUT_FILE"

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Conversion completed successfully!"
    echo ""
    echo "Files created:"
    echo "- $OUTPUT_FILE (combined)"
    echo "- ${OUTPUT_FILE%.sql}_schema.sql (schema only)"
    echo "- ${OUTPUT_FILE%.sql}_data.sql (data only)"
    echo ""
    echo "To check if data exists:"
    echo "grep -c 'INSERT INTO' $OUTPUT_FILE"
    echo "grep -c 'COPY.*FROM stdin' $OUTPUT_FILE"
    echo ""
    echo "To import the SQL file:"
    echo "psql -d your_database_name -f $OUTPUT_FILE"
else
    echo "❌ Conversion failed!"
fi
