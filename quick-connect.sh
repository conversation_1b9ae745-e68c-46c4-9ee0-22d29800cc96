#!/bin/bash

# Quick connection shortcuts for teamsphere VM

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== TeamSphere VM Quick Connect ===${NC}"
echo ""
echo "Choose connection option:"
echo "1) Connect as current user ($(whoami))"
echo "2) Connect as vbs_tms user"
echo "3) Connect as piyush_mishra user"
echo "4) Check VM status"
echo "5) Start VM"
echo "6) Get VM IP"
echo "7) Upload project files to vbs_tms"
echo "8) Upload project files to piyush_mishra"
echo "9) Execute custom command"
echo "0) Exit"
echo ""

read -p "Enter your choice (0-9): " choice

case $choice in
    1)
        echo -e "${GREEN}Connecting as $(whoami)...${NC}"
        ./connect-vm.sh connect
        ;;
    2)
        echo -e "${GREEN}Connecting as vbs_tms...${NC}"
        ./connect-vm.sh connect vbs_tms
        ;;
    3)
        echo -e "${GREEN}Connecting as piyush_mishra...${NC}"
        ./connect-vm.sh connect piyush_mishra
        ;;
    4)
        echo -e "${GREEN}Checking VM status...${NC}"
        ./connect-vm.sh status
        ;;
    5)
        echo -e "${GREEN}Starting VM...${NC}"
        ./connect-vm.sh start
        ;;
    6)
        echo -e "${GREEN}Getting VM IP...${NC}"
        ./connect-vm.sh ip
        ;;
    7)
        echo -e "${GREEN}Uploading project to vbs_tms...${NC}"
        read -p "Enter local path (default: /home/<USER>/Documents/teamsphere): " local_path
        local_path=${local_path:-"/home/<USER>/Documents/teamsphere"}
        ./connect-vm.sh upload "$local_path" "/home/<USER>/" vbs_tms
        ;;
    8)
        echo -e "${GREEN}Uploading project to piyush_mishra...${NC}"
        read -p "Enter local path (default: /home/<USER>/Documents/teamsphere): " local_path
        local_path=${local_path:-"/home/<USER>/Documents/teamsphere"}
        ./connect-vm.sh upload "$local_path" "/home/<USER>/" piyush_mishra
        ;;
    9)
        read -p "Enter command to execute: " command
        read -p "Enter username (default: vbs_tms): " username
        username=${username:-"vbs_tms"}
        echo -e "${GREEN}Executing: $command${NC}"
        ./connect-vm.sh exec "$command" "$username"
        ;;
    0)
        echo "Goodbye!"
        exit 0
        ;;
    *)
        echo "Invalid choice. Please try again."
        ;;
esac
