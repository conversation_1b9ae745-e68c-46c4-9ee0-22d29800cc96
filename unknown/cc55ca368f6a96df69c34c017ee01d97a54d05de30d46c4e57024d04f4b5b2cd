.filter-form {
  padding: 16px;
}

.filter-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
  max-width: 800px;
}

.filter-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-section h3 {
  margin: 0;
  color: rgba(0, 0, 0, 0.87);
  font-size: 16px;
  font-weight: 500;
}

.date-range {
  grid-column: 1 / -1;
}

.date-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

mat-form-field {
  width: 100%;
}

::ng-deep .mat-dialog-container {
  padding: 0;
}

::ng-deep .mat-dialog-content {
  max-height: 80vh;
  padding: 0;
}

::ng-deep .mat-dialog-actions {
  padding: 16px 24px;
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}

::ng-deep .mat-dialog-title {
  margin: 0;
  padding: 16px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  font-size: 20px;
  font-weight: 500;
}

::ng-deep .mat-option {
  height: 36px;
  line-height: 36px;
  padding: 0 16px;
}

::ng-deep .mat-option:hover {
  background: rgba(0, 0, 0, 0.04);
}

::ng-deep .mat-option.mat-selected:not(.mat-option-multiple) {
  background: rgba(0, 0, 0, 0.08);
} 