.admin-dashboard {
  padding: 20px;
}

.actions {
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.actions button {
  margin-right: 20px; /* Adds space between the Add User button and search bar */
}

.mat-elevation-z8 {
  width: 100%;
}

/* Add spacing between buttons */
.action-button {
  margin-right: 8px; /* Space between buttons */
}

/* Remove margin from the last button */
.action-button:last-child {
  margin-right: 0;
}

/* Style for the search field */
.search-field {
  width: 400px; /* Adjust width to make it longer */
  height: 30px; /* Reduce height to make it shorter */
  margin-left: 16px; /* Add spacing to separate it from the "Add User" button */
  margin-bottom: 30px;
}

.search-field input {
  height: 10px; /* Control the height of the input box inside the field */
  padding: 4px 8px; /* Adjust padding for better fit */
  font-size: 14px; /* Adjust font size for better readability */
}

/* General <PERSON><PERSON> Styles */
.mat-raised-button:hover {
  background-color: #9e9e9e !important; /* Darker grey */
  color: #fff !important; /* White text for contrast */
}

/* Primary Button Hover */
.mat-raised-button[color="primary"]:hover {
  background-color: #9e9e9e !important; /* Darker grey */
  color: #fff !important; /* White text for contrast */
}

/* Accent Button Hover */
.mat-raised-button[color="accent"]:hover {
  background-color: #9e9e9e !important; /* Darker grey */
  color: #fff !important; /* White text for contrast */
}

/* Warn Button Hover */
.mat-raised-button[color="warn"]:hover {
  background-color: #d32f2f !important; /* Darker red */
  color: #fff !important; /* White text for contrast */
}

/* Action Buttons Specific Hover Styles */
.action-button.view-button:hover {
  background-color: #444444 !important; /* Dark grey for 'View' */
  color: #fff !important; /* White text for visibility */
}

.action-button.edit-button:hover {
  background-color: #3b5998 !important; /* Darker blue for 'Edit' */
  color: #fff !important; /* White text for visibility */
}

.action-button.delete-button:hover {
  background-color: #b71c1c !important; /* Darker red for 'Delete' */
  color: #fff !important; /* White text for warning actions */
}

.action-button.upload-button:hover {
  background-color: #1cb536 !important; /* Darker blue for 'Edit' */
  color: #fff !important; /* White text for visibility */
}

/* Search Field Icon Styling on Hover */
.search-field mat-icon:hover {
  color: #000 !important; /* Black icon for visibility */
}

/* Checkbox Column Hover */
.mat-column-select:hover {
  background-color: #dcdcdc !important; /* Slightly darker background */
  color: #000 !important; /* Black text */
}

/* Action Button Specific Styles */
.action-button {
  margin-right: 8px; /* Space between action buttons */
}

.action-button:last-child {
  margin-right: 0;
}

.action-button.view-button {
  background-color: #fff !important; /* White for 'View' */
  color: #000 !important; /* Black text for visibility */
}

.action-button.edit-button {
  background-color: #fff !important; /* Slightly grey for 'Edit' */
  color: #0423bcf7 !important; /* Black text for visibility */
}

.action-button.delete-button {
  background-color: #fff !important; /* Light grey for 'Delete' */
  color: #d32f2f !important; /* Red text for warning actions */
}

/* Add Button */
.action-button.add-button {
  background-color: #fff !important; /* Light blue */
  color: #005cbf !important; /* Dark blue text */
}

.action-button.add-button:hover {
  background-color: #005cbf !important; /* Dark blue */
  color: #fff !important; /* White text */
}

/* Upload Button */
.action-button.upload-button {
  background-color: #fff !important; /* Light green */
  color: #228b22 !important; /* Dark green text */
}

.action-button.upload-button:hover {
  background-color: #228b22 !important; /* Dark green */
  color: #fff !important; /* White text */
}

/* Download Button */
.action-button.download-button {
  background-color: #fff !important; /* Light orange */
  color: #e67e22 !important; /* Orange text */
}

.action-button.download-button:hover {
  background-color: #e67e22 !important; /* Dark orange */
  color: #fff !important; /* White text */
}

.action-button.bitrix-button {
    background-color: #fff !important; /* Google blue color */
    color: #4285f4 !important; /* White text for visibility */
  }

/* Add this to your existing CSS */
.action-button.bitrix-button:hover {
    background-color: #4285f4 !important; /* Google blue color */
    color: #fff !important; /* White text for visibility */
  }

/* Info Button */
.action-button.info-button {
  background-color: #fff !important; /* Light orange */
  color: #040404 !important; /* Orange text */
}

.action-button.info-button:hover {
  background-color: #040404 !important; /* Dark orange */
  color: #fff !important; /* White text */
}

/* Search Field Icon Styling */
.search-field mat-icon {
  color: #757575 !important; /* Dark grey for icon */
}

/* Force the checkbox column to have a minimal width */
.mat-column-select {
  width: 150px !important; /* Enforce small width */
  max-width: 150px !important;
  text-align: center; /* Center the checkbox */
}

.header-csv {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-csv h2 {
  margin: 0; /* To remove default margins */
}

.mat-dialog-actions {
  margin: 0;
  padding: 0;
}

.column-toggle {
  width: 250px;
  margin-right: 20px;
}

.upload-container {
  display: flex;
  align-items: center;
  gap: 8px; /* Space between button and icon */
}

.info-icon {
  cursor: pointer;
  color: #666;
}
.info-icon:hover {
  color: #000;
}

/* Ensure proper spacing and alignment */
.column-toggle {
  width: auto;
  margin-left: 12px;
  margin-top: 20px;
  height: 70px;
}

.header-container {
  display: flex;
  flex-direction: column; /* Stack items vertically */
  align-items: center; /* Center align content */
  gap: 5px; /* Add spacing between header text and input */
}

.header-text {
  font-weight: bold;
}

.filter-input {
  width: 100%; /* Full width for better alignment */
  padding: 5px;
  font-size: 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
}

mat-table {
 width: 1200px;
 max-width: 1200px;
 margin-bottom: 1rem;
 display: table;
 border-collapse: collapse;
 margin: 0px;
}

mat-row,
mat-header-row {
  display: table-row;
}
mat-cell,
mat-header-cell {
  word-wrap: initial;
  display: table-cell;
  padding: 0px 5px;
  line-break: unset;
  width: auto;
  white-space: nowrap;
  overflow: hidden;
  vertical-align: middle;
}

.duplicate-row {
  background-color: #ffdddd;
}

/* Dropdown Container */
.dropdown {
  position: relative;
  display: inline-block;
  font-family: 'Inter', sans-serif;
}

/* Button Styling (No Border) */
.dropdown-button {
  background-color: #fffdfd00 !important;
  color: #256fa0 !important;
  font-weight: bold; /* Bold text */
  padding: 10px 16px;
  border: none; /* Removed border */
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 8px; /* Space between icon and text */
}

/* Button Hover Effect */
.dropdown-button:hover {
  background: #075b92 !important;
  color: #fff !important;  /* Ensure text remains visible */
}

/* Dropdown Menu */
.dropdown-menu {
  position: absolute;
  top: 110%;
  left: 0;
  background: white;
  border-radius: 10px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  width: 250px;
  padding: 10px;
  z-index: 1000;
  opacity: 0;
  transform: translateY(-10px);
  animation: dropdownFadeIn 0.3s forwards;
}

/* Animation */
@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Search Box */
.search-box {
  width: 90%;
  padding: 8px;
  margin-bottom: 8px;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.3s;
}

.search-box:focus {
  border-color: #007bff;
}

/* Select All Checkbox */
.checkbox-container {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  padding: 6px 0;
  user-select: none;
}

.checkbox-container input {
  cursor: pointer;
  accent-color: #007bff;
  width: 16px;
  height: 16px;
}

/* Column List */
.column-list {
  max-height: 200px;
  overflow-y: auto;
  padding-right: 5px;
}

/* Scrollbar Styling */
.column-list::-webkit-scrollbar {
  width: 5px;
}

.column-list::-webkit-scrollbar-thumb {
  background-color: #ccc;
  border-radius: 5px;
}

.column-list::-webkit-scrollbar-track {
  background: transparent;
}

/* Column Item */
.column-list label {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 0;
  transition: background 0.2s;
  border-radius: 5px;
}

.column-list label:hover {
  background: #f5f5f5;
}

/* CSV Format Table */
.csv-format-table {
  width: 100%;
  margin-top: 15px;
  margin-bottom: 15px;
}

.csv-format-table .mat-header-cell {
  font-weight: bold;
  background-color: #f5f5f5;
}

.csv-format-table .mat-row:nth-child(even) {
  background-color: #f9f9f9;
}

.csv-format-table .mat-cell,
.csv-format-table .mat-header-cell {
  padding: 8px 12px;
}

