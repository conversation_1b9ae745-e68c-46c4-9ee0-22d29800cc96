/* General Container Styles */
.request-form-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh; /* Use height instead of min-height */
  background-color: #f4f6f8;
  padding: 0; /* Remove extra padding */
  margin: 0; /* Remove default margin */
  box-sizing: border-box;
  overflow: hidden; /* Prevent scrolling */
}
  
.form-card {
  max-width: 600px;
  width: 100%;
  max-height: 90vh; /* Ensure the card does not exceed the viewport height */
  overflow-y: auto; /* Internal scrolling for content, if necessary */
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  background-color: #ffffff;
}
  
  mat-card-header {
    background-color: #3f51b5;
    color: #ffffff;
    padding: 16px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
  }
  
  mat-card-title {
    font-size: 1.5rem;
    font-weight: bold;
  }
  
  mat-card-subtitle {
    font-size: 1rem;
    opacity: 0.8;
  }
  
  /* Form Field Styles */
  mat-form-field {
    display: block;
    margin-bottom: 16px;
  }
  
  .full-width {
    width: 100%;
  }
  
  /* Input and Textarea Styles */
  textarea[matInput] {
    resize: none;
  }
  
  /* Button Styles */
  .form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 16px;
  }
  
  button[mat-raised-button] {
    font-size: 1rem;
    padding: 10px 20px;
    text-transform: uppercase;
  }
  
  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .form-card {
      padding: 16px;
    }
  
    mat-card-header {
      text-align: center;
    }
  
    .form-actions {
      justify-content: center;
    }
  }
  