package com.vbs.capsAllocation.dto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AssignProjectsToUserDTO {
    private Long userId;
    private List<Long> projectIds;
    private LocalDate assignedDate;
    private String status;
}