.assignment-form-container {
  padding: 24px;
  max-width: 600px;
  margin: 0 auto;
}

.full-width {
  width: 100%;
  margin-bottom: 16px;
}

mat-dialog-actions {
  padding: 16px 0;
  margin-bottom: 0;
}

mat-hint {
  color: rgba(0, 0, 0, 0.6);
}

/* Style for the dialog title */
h2.mat-dialog-title {
  margin-top: 0;
  color: #333;
  font-weight: 500;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

/* Style for the submit button */
button[type="submit"] {
  background-color: #fff !important;
  color: #005cbf !important;
}

button[type="submit"]:hover:not([disabled]) {
  background-color: #005cbf !important;
  color: #fff !important;
}

button[type="submit"]:disabled {
  background-color: #f5f5f5 !important;
  color: #bdbdbd !important;
}

/* Style for the cancel button */
button[type="button"] {
  color: #757575;
}

button[type="button"]:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.select-all-container {
  padding: 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.select-all-button {
  width: 100%;
  justify-content: flex-start;
  text-align: left;
}

.select-all-button mat-icon {
  margin-right: 8px;
}

.search-box {
  padding: 8px;
  position: sticky;
  top: 0;
  background: white;
  z-index: 1;
}

.search-field {
  width: 100%;
}

.search-field ::ng-deep .mat-form-field-wrapper {
  margin: 0;
  padding: 0;
}

.search-field ::ng-deep .mat-form-field-infix {
  border-top: none;
  padding: 8px 0;
}

.search-field ::ng-deep .mat-form-field-underline {
  display: none;
}

::ng-deep .mat-select-panel {
  max-height: 350px !important;
}

::ng-deep .mat-select-search-input {
  padding: 8px;
}

::ng-deep .mat-select-search {
  padding: 8px;
  background: white;
  position: sticky;
  top: 0;
  z-index: 1;
}
