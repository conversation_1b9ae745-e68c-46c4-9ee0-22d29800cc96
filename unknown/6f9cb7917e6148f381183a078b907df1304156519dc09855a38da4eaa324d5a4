.approval-table-container {
    width: 100%;
    overflow-x: auto;
    margin: 20px 0;
  }

  table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    table-layout: auto; /* Allow columns to auto adjust based on content */
  }

  th, td {
    padding: 10px 15px;
    text-align: left;
    word-wrap: break-word;
  }

  th {
    background-color: #f5f5f5;
    font-weight: bold;
  }

  .mat-raised-button {
    margin-right: 8px;
    padding: 8px 16px;
  }

  .action-button {
    transition: background-color 0.3s ease;
    font-size: 14px;
    font-weight: 500;
  }

  .action-button:hover {
    background-color: #9e9e9e !important;
    color: #fff !important;
  }

  .action-button.view-button:hover {
    background-color: #444444 !important;
  }

  .action-button.edit-button:hover {
    background-color: #3b5998 !important;
  }

  .action-button.delete-button:hover {
    background-color: #b71c1c !important;
  }

  .action-button.add-button:hover {
    background-color: #005cbf !important;
  }

  .action-button.upload-button:hover {
    background-color: #1cb536 !important;
  }

  .action-button.download-button:hover {
    background-color: #e67e22 !important;
  }

  /* Specific styles for action buttons */
  .actions {
    display: flex;
    justify-content: flex-start; /* Align buttons to the left */
    align-items: center;
    gap: 10px; /* Adds space between the buttons */
    flex-wrap: nowrap; /* Prevent wrapping */
    flex-shrink: 0; /* Prevent buttons from shrinking */
  }

  .action-button {
    padding: 8px 16px;
    font-size: 14px;
    white-space: nowrap; /* Prevent text from wrapping inside buttons */
  }

  .mat-header-cell, .mat-cell {
    padding: 12px 15px;
  }

  tr.mat-row {
    border-bottom: 1px solid #e0e0e0;
  }

  tr.mat-header-row {
    border-bottom: 2px solid #ccc;
  }

  .action-button {
    margin: 5px;
  }

  button[color="success"] {
    background-color: #4caf50; /* Green */
    color: white;
  }

  button[color="warn"] {
    background-color: #f44336; /* Red */
    color: white;
  }

  /* Style for filter container to ensure input appears below header */
/* Ensure header text and input field are stacked vertically */
.header-container {
  display: flex;
  flex-direction: column;
  align-items: center; /* Center align content */
  text-align: center;
}

/* Style the filter input field */
.filter-container {
  width: 100%; /* Full column width */
  margin-top: 8px; /* Spacing between header text and input */
}

.filter-container input {
  width: calc(100% - 16px); /* Adjust width to fit padding */
  padding: 6px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
}

.filter-options {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  gap: 20px;
}

.my-requests-toggle {
  margin-left: 20px;
}

.advanced-filters {
  margin-bottom: 10px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
}

.date-range-container {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.apply-filter-btn, .reset-filter-btn {
  height: 36px;
  margin-top: 4px;
}

.global-search {
  position: relative;
  margin-top: 15px;
  margin-bottom: 15px;
  width: 100%;
  max-width: 400px;
  float: right;
  margin-left: auto;
}

.global-search mat-form-field {
  width: 100%;
}

/* Input field styles */
.global-search input {
  width: 100%;
  height: 9px; /* Adjust height */
  padding: 8px 12px;
  font-size: 14px;
  border-radius: 6px;
}

/* Search icon styling */
.global-search mat-icon {
  position: absolute;
  right: 12px;
  top: 100%;
  transform: translateY(-50%);
  color: #757575;
  cursor: pointer;
  transition: color 0.3s ease;
}

.global-search mat-icon:hover {
  color: #000 !important; /* Darker color on hover */
}

/* Focus effect on input field */
.global-search input:focus {
  border-color: #007bff !important;
  outline: none;
}



