.time-summary-container {
  padding: 20px;
}

.actions {
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.actions button {
  margin-right: 20px;
}

.mat-elevation-z8 {
  width: 100%;
}

/* Add spacing between buttons */
.action-button {
  margin-right: 8px;
}

/* Remove margin from the last button */
.action-button:last-child {
  margin-right: 0;
}

/* Style for the search field */
.search-field {
  width: 400px;
  height: 30px;
  margin-left: 16px;
  margin-bottom: 30px;
}

.search-field input {
  height: 10px;
  padding: 4px 8px;
  font-size: 14px;
}

/* General Button Hover Styles */
.mat-raised-button:hover {
  background-color: #9e9e9e !important;
  color: #fff !important;
}

/* Primary Button Hover */
.mat-raised-button[color="primary"]:hover {
  background-color: #9e9e9e !important;
  color: #fff !important;
}

/* Accent Button Hover */
.mat-raised-button[color="accent"]:hover {
  background-color: #9e9e9e !important;
  color: #fff !important;
}

/* Warn Button Hover */
.mat-raised-button[color="warn"]:hover {
  background-color: #d32f2f !important;
  color: #fff !important;
}

/* Download Button */
.action-button.download-button {
  background-color: #fff !important;
  color: #e67e22 !important;
}

.action-button.download-button:hover {
  background-color: #e67e22 !important;
  color: #fff !important;
}

/* Toggle Filter Button */
.action-button.toggle-filter-button {
  background-color: #fff !important;
  color: #0c0c0c !important;
}

.action-button.toggle-filter-button:hover {
  background-color: #fff !important;
  color: #0c0c0c !important;
}

/* Search Field Icon Styling */
.search-field mat-icon {
  color: #757575 !important;
}

/* Header Container in Cells */
.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 8px;
}

.header-text {
  font-weight: bold;
}

.filter-input {
  width: 100%;
  padding: 5px;
  font-size: 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
}

mat-table {
  width: 1200px;
  max-width: 1200px;
  margin-bottom: 1rem;
  display: table;
  border-collapse: collapse;
  margin: 0px;
}

mat-row,
mat-header-row {
  display: table-row;
}

mat-cell,
mat-header-cell {
  word-wrap: initial;
  display: table-cell;
  padding: 0px 5px;
  line-break: unset;
  width: auto;
  white-space: nowrap;
  overflow: hidden;
  vertical-align: middle;
}

/* Row Hover Effect */
.mat-mdc-row:hover {
  background-color: #f8f9fa;
  transition: background-color 0.2s ease;
}

/* Loading Spinner */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 1000;
}

/* Header Controls */
.header-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.left-controls {
  display: flex;
  align-items: center;
}

.right-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* Filter Menu */
.custom-filter-menu {
  min-width: 250px;
}

.filter-search-field {
  width: 100%;
  margin-bottom: 8px;
}

/* No Data Row */
.mat-mdc-row.no-data-row {
  display: flex;
  justify-content: center;
  padding: 24px;
  text-align: center;
  color: #6c757d;
  font-style: italic;
}