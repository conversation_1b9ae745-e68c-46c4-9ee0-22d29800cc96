/* Outer container covers full viewport */
.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh; /* Full viewport height */
  box-sizing: border-box;
}

/* Top half: charts row split into two equal columns */
.charts-row {
  display: flex;
  height: 50vh; /* Top half of the viewport */
  gap: 20px;
  padding: 10px;
  box-sizing: border-box;
}

.chart-item {
  flex: 1;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  position: relative;
  padding-top: 40px; /* Make space for the header */
}

/* Column Controls */
.column-controls {
  display: flex;
  justify-content: flex-end;
  padding: 10px;
  margin: 10px;
}

.column-toggle {
  display: flex;
  align-items: center;
}

.column-menu-button {
  display: flex;
  align-items: center;
  gap: 8px;
}

.column-menu-button mat-icon {
  margin-right: 8px;
}

/* Column Menu */
.column-menu {
  min-width: 250px;
  max-height: 400px;
  overflow: hidden;
}

/* Search Box */
.search-box {
  padding: 8px;
  background-color: white;
  position: sticky;
  top: 0;
  z-index: 1;
}

.search-field {
  width: 100%;
}

.search-field ::ng-deep .mat-form-field-wrapper {
  margin: 0;
  padding: 0;
}

.search-field ::ng-deep .mat-form-field-infix {
  border-top: none;
  padding: 8px 0;
}

/* Select All Container */
.select-all-container {
  padding: 8px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

/* Column List */
.column-list {
  max-height: 300px;
  overflow-y: auto;
  padding: 8px 0;
}

.column-item {
  padding: 8px 16px;
  display: flex;
  align-items: center;
}

.column-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.column-item mat-checkbox {
  width: 100%;
}

/* Table Container */
.table-container {
  overflow-x: auto;
  margin: 10px;
  padding: 10px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Table Styles */
table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
}

th, td {
  padding: 8px;
  border: 1px solid #ddd;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
  min-width: 50px;
}

th {
  background-color: #f5f5f5;
  cursor: pointer;
}

/* Resizable Columns */
th.resizable {
  position: relative;
  user-select: none;
}

.column-content {
  padding-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.resize-handle {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 8px;
  background-color: transparent;
  cursor: col-resize;
  z-index: 1;
}

.resize-handle::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 1px;
  background-color: #ddd;
}

.resize-handle:hover::after {
  background-color: #999;
  width: 2px;
}

.resize-handle:active::after {
  background-color: #666;
  width: 2px;
}

/* Column Header Styles */
.column-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.column-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

.collapse-button {
  width: 24px;
  height: 24px;
  padding: 0;
  line-height: 24px;
}

/* Drag and Drop Styles */
th.cdk-drag-preview {
  background-color: #e3f2fd;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

th.dragging {
  opacity: 0.5;
}

/* Collapse Styles */
th.collapsed, td.collapsed {
  width: 40px;
  padding: 8px 4px;
}

th.collapsed .column-title {
  display: none;
}

/* First column styling */
tr td:first-child,
tr th:first-child {
  text-align: left;
  background-color: #f5f5f5;
}

/* Total row styling */
tr:last-child td {
  font-weight: bold;
  background-color: #f5f5f5;
}

/* Total column styling */
td:last-child,
th:last-child {
  background-color: #f5f5f5;
  font-weight: bold;
}

/* Hover effects */
tbody tr:hover {
  background-color: #f5f8ff;
}

/* Responsive design */
@media screen and (max-width: 768px) {
  .table-row {
    margin: 10px 0;
    border-radius: 6px;
  }

  th, td {
    padding: 8px 10px;
    font-size: 13px;
  }
}

/* Numbers alignment */
td:not(:first-child) {
  text-align: right;
}

/* Zebra striping */
tbody tr:nth-child(even) {
  background-color: #fafbfc;
}

/* Chart Header */
.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1;
}

.chart-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  flex: 1;
}

.fullscreen-button {
  color: #444;
  transition: all 0.2s ease;
  margin-left: 8px;
  background-color: rgba(0, 0, 0, 0.05);
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  padding: 0;
}

.fullscreen-button:hover {
  color: #000;
  background-color: rgba(0, 0, 0, 0.1);
}

.fullscreen-button mat-icon {
  font-size: 22px;
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Custom Column Icon */
.column-icon {
  display: flex;
  flex-direction: column;
  gap: 3px;
  width: 20px;
  height: 20px;
}

.column-icon-bar {
  width: 100%;
  height: 4px;
  background-color: #666;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.column-menu-button:hover .column-icon-bar {
  background-color: #333;
}

/* Fullscreen Styles */
.chart-item.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
  background-color: white;
  margin: 0;
  padding: 0;
  border: none;
  border-radius: 0;
}

.chart-item.fullscreen .chart-header {
  position: sticky;
  top: 0;
  z-index: 1001;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-item.fullscreen highcharts-chart {
  height: calc(100vh - 50px) !important; /* Subtract header height */
  width: 100% !important;
}

/* When a chart is in fullscreen, hide the other chart */
.chart-item.fullscreen ~ .chart-item:not(.fullscreen) {
  display: none;
}

/* Show both charts when none are in fullscreen */
.chart-item:not(.fullscreen) {
  display: flex;
}


