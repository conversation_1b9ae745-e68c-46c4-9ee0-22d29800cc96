.password-reset-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
  background-color: #f5f5f5;
  position: fixed;
  top: -300px;
  left: 0;
  right: 0;
  bottom: 0;
}

.logo {
  width: 200px;
  margin-bottom: 30px;
}

h2 {
  color: #134472;
  margin-bottom: 10px;
  font-size: 24px;
}

.reset-subtitle {
  color: #666;
  margin-bottom: 30px;
  font-size: 16px;
}

form {
  width: 100%;
  max-width: 400px;
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
}

.custom-field {
  width: 100%;
  margin-bottom: 20px;
}

.buttons {
  margin-top: 30px;
}

.action-button {
  width: 100%;
  padding: 8px;
  font-size: 16px;
}

::ng-deep .mat-form-field-appearance-fill .mat-form-field-flex {
  background-color: #f8f9fa;
}
