spring.datasource.url=****************************************************
spring.datasource.username=postgres
spring.datasource.password=voyage
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.web.resources.static-locations=file:uploads/profile-pics
spring.servlet.multipart.max-file-size=1MB
spring.servlet.multipart.max-request-size=1MB
google.sheets.spreadsheetId=14USVFIoRJQzSgKA9_DI3_c_rOJy7G2W9txJRNRDCfAw
google.sheets.credentials=classpath:ops-excellence-a969197613f8.json
google.sheets.sheetName=Response
logging.level.root=INFO
logging.level.org.springframework=INFO
logging.level.com.vbs.capsAllocation=DEBUG
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=lloe ahpm qbpu nzuh
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true


spring.jpa.properties.hibernate.default_schema=public



