.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px;
    border-radius: 10px;
    color: white;
    font-weight: bold;
    z-index: 9999;
    opacity: 0;
    transform: translateY(-20px);
    animation: slideIn 0.5s forwards, fadeOut 3s 4.5s forwards;
  }
  
  @keyframes slideIn {
    0% {
      opacity: 0;
      transform: translateY(-20px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes fadeOut {
    0% {
      opacity: 1;
    }
    100% {
      opacity: 0;
    }
  }
  
  .success {
    background-color: #28a745;
    box-shadow: 0px 4px 10px rgba(40, 167, 69, 0.5);
  }
  
  .error {
    background-color: #dc3545;
    box-shadow: 0px 4px 10px rgba(220, 53, 69, 0.5);
  }
  
  .info {
    background-color: #17a2b8;
    box-shadow: 0px 4px 10px rgba(23, 162, 184, 0.5);
  }
  
  .warning {
    background-color: #ffc107;
    box-shadow: 0px 4px 10px rgba(255, 193, 7, 0.5);
  }
  
  /* Add smooth transition effects when showing and hiding the notification */
  .notification span {
    display: inline-block;
    animation: fadeInText 0.5s ease-in-out forwards;
  }
  
  @keyframes fadeInText {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
  