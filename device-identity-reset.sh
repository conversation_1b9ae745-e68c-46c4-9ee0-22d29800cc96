#!/bin/bash

# Device Identity Reset Script
# This script clears all device fingerprinting data

set -e

echo "🔄 Starting Device Identity Reset..."

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 1. Clear Browser Data
echo "🌐 Clearing browser data..."

# Chrome/Chromium
if [ -d ~/.config/google-chrome ]; then
    echo "  - Clearing Chrome data..."
    rm -rf ~/.config/google-chrome/Default/Local\ Storage/ 2>/dev/null || true
    rm -rf ~/.config/google-chrome/Default/Session\ Storage/ 2>/dev/null || true
    rm -rf ~/.config/google-chrome/Default/IndexedDB/ 2>/dev/null || true
    rm -rf ~/.config/google-chrome/Default/Cookies* 2>/dev/null || true
    rm -rf ~/.config/google-chrome/Default/Web\ Data* 2>/dev/null || true
    rm -rf ~/.config/google-chrome/Default/History* 2>/dev/null || true
fi

# Chromium
if [ -d ~/.config/chromium ]; then
    echo "  - Clearing Chromium data..."
    rm -rf ~/.config/chromium/Default/Local\ Storage/ 2>/dev/null || true
    rm -rf ~/.config/chromium/Default/Session\ Storage/ 2>/dev/null || true
    rm -rf ~/.config/chromium/Default/IndexedDB/ 2>/dev/null || true
    rm -rf ~/.config/chromium/Default/Cookies* 2>/dev/null || true
fi

# Firefox
if [ -d ~/.mozilla/firefox ]; then
    echo "  - Clearing Firefox data..."
    find ~/.mozilla/firefox -name "storage" -type d -exec rm -rf {} + 2>/dev/null || true
    find ~/.mozilla/firefox -name "sessionstore*" -exec rm -f {} + 2>/dev/null || true
    find ~/.mozilla/firefox -name "cookies.sqlite*" -exec rm -f {} + 2>/dev/null || true
fi

# 2. Clear VS Code Data
echo "💻 Clearing VS Code data..."
rm -rf ~/.vscode/extensions/ 2>/dev/null || true
rm -rf ~/.config/Code/User/globalStorage/ 2>/dev/null || true
rm -rf ~/.config/Code/User/workspaceStorage/ 2>/dev/null || true
rm -rf ~/.config/Code/logs/ 2>/dev/null || true

# 3. Clear System Caches and Identifiers
echo "🗂️  Clearing system caches..."
rm -rf ~/.cache/ 2>/dev/null || true
rm -rf ~/.local/share/recently-used.xbel 2>/dev/null || true

# Clear fontconfig cache
if command_exists fc-cache; then
    fc-cache -f -v >/dev/null 2>&1 || true
fi

# 4. Generate new machine ID (requires sudo)
echo "🔑 Generating new machine ID..."
if [ "$EUID" -eq 0 ]; then
    rm -f /etc/machine-id
    systemd-machine-id-setup
    echo "  - New machine ID generated"
else
    echo "  - Skipping machine ID reset (requires sudo)"
    echo "  - Run: sudo rm /etc/machine-id && sudo systemd-machine-id-setup"
fi

# 5. Clear additional tracking data
echo "🧹 Clearing additional tracking data..."
rm -rf ~/.pki/ 2>/dev/null || true
rm -rf ~/.dbus/ 2>/dev/null || true

echo "✅ Device identity reset complete!"
echo ""
echo "📋 Next steps:"
echo "1. Restart your browser"
echo "2. Restart VS Code"
echo "3. Consider using VPN/proxy for IP masking"
echo "4. Use incognito/private browsing mode"
