# TeamSphere Production Environment Configuration
# This file contains production-specific environment variables

# Docker Configuration
DOCKER_USERNAME=your-dockerhub-username
IMAGE_NAME=teamsphere
TAG=latest

# Database Configuration
POSTGRES_PASSWORD=voyage_prod_secure_password
POSTGRES_DB=vbs_allocation_caps
POSTGRES_USER=postgres

# AWS SES Configuration
AWS_SES_ACCESS_KEY=********************
AWS_SES_SECRET_KEY=ag2ezW/wESE5X0BFLrCUUf+evzfpGUqMTavvJRRS
AWS_SES_REGION=us-east-1
AWS_SES_FROM_EMAIL=<EMAIL>

# Application Configuration
JAVA_OPTS=-Xmx2g -Xms1g -XX:+UseG1GC -XX:MaxGCPauseMillis=200
SERVER_PORT=8080

# Security Configuration
JWT_SECRET=your-production-jwt-secret-key-here-make-it-long-and-secure

# Backup Configuration
BACKUP_SHARE_EMAIL=<EMAIL>

# Google Sheets Configuration
GOOGLE_SHEETS_SPREADSHEET_ID=14USVFIoRJQzSgKA9_DI3_c_rOJy7G2W9txJRNRDCfAw
GOOGLE_SHEETS_SHEET_NAME=Response
