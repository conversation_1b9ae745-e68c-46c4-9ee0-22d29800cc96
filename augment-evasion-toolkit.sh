#!/bin/bash

# Augment Code Evasion Toolkit
# Complete device identity reset and fingerprint evasion script
# This script will make your machine appear as a completely new device to augment code

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root for complete device identity reset"
        error "Please run: sudo $0"
        exit 1
    fi
}

# Install required packages
install_dependencies() {
    log "Installing required dependencies..."
    
    # Update package lists
    apt update -y
    
    # Install required packages
    apt install -y \
        macchanger \
        ethtool \
        dmidecode \
        lshw \
        uuid-runtime \
        curl \
        wget \
        jq
    
    log "Dependencies installed successfully"
}

# Reset machine ID
reset_machine_id() {
    log "Resetting machine ID..."
    
    # Backup original machine-id
    if [[ -f /etc/machine-id ]]; then
        cp /etc/machine-id /etc/machine-id.backup.$(date +%s)
    fi
    
    # Remove and regenerate machine-id
    rm -f /etc/machine-id
    systemd-machine-id-setup
    
    # Also reset dbus machine-id
    rm -f /var/lib/dbus/machine-id
    dbus-uuidgen --ensure=/var/lib/dbus/machine-id
    
    log "Machine ID reset complete"
}

# Randomize MAC addresses
randomize_mac_addresses() {
    log "Randomizing MAC addresses..."
    
    # Get all network interfaces
    interfaces=$(ip link show | grep -E '^[0-9]+:' | cut -d: -f2 | tr -d ' ' | grep -v lo)
    
    for interface in $interfaces; do
        if [[ -d /sys/class/net/$interface ]]; then
            log "Randomizing MAC for interface: $interface"
            
            # Bring interface down
            ip link set dev "$interface" down
            
            # Generate random MAC address
            new_mac=$(printf '%02x:%02x:%02x:%02x:%02x:%02x\n' $((RANDOM%256)) $((RANDOM%256)) $((RANDOM%256)) $((RANDOM%256)) $((RANDOM%256)) $((RANDOM%256)) | sed 's/^\(.\)[13579bdf]/\10/')
            
            # Set new MAC address
            macchanger -m "$new_mac" "$interface"
            
            # Bring interface back up
            ip link set dev "$interface" up
            
            log "MAC address for $interface changed to: $new_mac"
        fi
    done
}

# Clear browser data
clear_browser_data() {
    log "Clearing browser data..."
    
    # Chrome/Chromium
    if [[ -d $HOME/.config/google-chrome ]]; then
        rm -rf $HOME/.config/google-chrome/Default/Local\ Storage/
        rm -rf $HOME/.config/google-chrome/Default/Session\ Storage/
        rm -rf $HOME/.config/google-chrome/Default/IndexedDB/
        rm -rf $HOME/.config/google-chrome/Default/Cookies*
        rm -rf $HOME/.config/google-chrome/Default/Web\ Data*
        rm -rf $HOME/.config/google-chrome/Default/History*
        rm -rf $HOME/.config/google-chrome/Default/Cache/
        log "Chrome data cleared"
    fi
    
    # Chromium
    if [[ -d $HOME/.config/chromium ]]; then
        rm -rf $HOME/.config/chromium/Default/Local\ Storage/
        rm -rf $HOME/.config/chromium/Default/Session\ Storage/
        rm -rf $HOME/.config/chromium/Default/IndexedDB/
        rm -rf $HOME/.config/chromium/Default/Cookies*
        rm -rf $HOME/.config/chromium/Default/Cache/
        log "Chromium data cleared"
    fi
    
    # Firefox
    if [[ -d $HOME/.mozilla/firefox ]]; then
        find $HOME/.mozilla/firefox -name "storage" -type d -exec rm -rf {} + 2>/dev/null || true
        find $HOME/.mozilla/firefox -name "cookies.sqlite*" -exec rm -f {} + 2>/dev/null || true
        find $HOME/.mozilla/firefox -name "webappsstore.sqlite*" -exec rm -f {} + 2>/dev/null || true
        log "Firefox data cleared"
    fi
}

# Clear system caches and identifiers
clear_system_caches() {
    log "Clearing system caches and identifiers..."
    
    # Clear user cache
    rm -rf $HOME/.cache/
    
    # Clear font cache
    fc-cache -f -v
    
    # Clear thumbnail cache
    rm -rf $HOME/.thumbnails/
    
    # Clear recent files
    rm -f $HOME/.local/share/recently-used.xbel
    
    # Clear SSH known hosts
    rm -f $HOME/.ssh/known_hosts
    
    # Clear GPG cache
    rm -rf $HOME/.gnupg/random_seed
    
    log "System caches cleared"
}

# Generate new SSH host keys
regenerate_ssh_keys() {
    log "Regenerating SSH host keys..."
    
    # Backup existing keys
    if [[ -d /etc/ssh ]]; then
        cp -r /etc/ssh /etc/ssh.backup.$(date +%s)
    fi
    
    # Remove existing keys
    rm -f /etc/ssh/ssh_host_*
    
    # Generate new keys
    ssh-keygen -A
    
    log "SSH host keys regenerated"
}

# Create browser fingerprint evasion
create_browser_fingerprint_evasion() {
    log "Setting up browser fingerprint evasion..."
    
    # Create browser launch script with fingerprint evasion
    cat > /usr/local/bin/augment-browser << 'EOF'
#!/bin/bash

# Browser launcher with fingerprint evasion for augment code
BROWSER_TYPE=$1

# Random user agents
USER_AGENTS=(
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0"
)

# Random screen resolutions
SCREEN_SIZES=(
    "1920,1080"
    "1366,768"
    "1440,900"
    "2560,1440"
)

# Random timezone
TIMEZONES=(
    "America/New_York"
    "America/Los_Angeles"
    "Europe/London"
    "Asia/Tokyo"
    "Australia/Sydney"
)

# Random language
LANGUAGES=(
    "en-US,en;q=0.9"
    "en-GB,en;q=0.9"
    "ja-JP,ja;q=0.9,en;q=0.8"
)

# Select random values
RANDOM_UA=${USER_AGENTS[$RANDOM % ${#USER_AGENTS[@]}]}
RANDOM_SIZE=${SCREEN_SIZES[$RANDOM % ${#SCREEN_SIZES[@]}]}
RANDOM_TZ=${TIMEZONES[$RANDOM % ${#TIMEZONES[@]}]}
RANDOM_LANG=${LANGUAGES[$RANDOM % ${#LANGUAGES[@]}]}

case $BROWSER_TYPE in
    "chrome")
        google-chrome \
            --user-agent="$RANDOM_UA" \
            --window-size=$RANDOM_SIZE \
            --disable-web-security \
            --disable-features=VizDisplayCompositor \
            --disable-background-timer-throttling \
            --disable-renderer-backgrounding \
            --incognito \
            --disable-extensions \
            --disable-plugins \
            --disable-default-apps \
            --disable-sync \
            --disable-background-networking \
            --disable-component-extensions-with-background-pages \
            --disable-extensions-http-throttling \
            --disable-ipc-flooding-protection \
            --disable-preconnect \
            --disable-web-resources \
            --disable-client-side-phishing-detection \
            --disable-component-update \
            --disable-domain-reliability \
            --disable-translate \
            --new-window &
        ;;
    "firefox")
        profile_dir="/tmp/firefox-augment-$(date +%s)"
        mkdir -p "$profile_dir"
        
        cat > "$profile_dir/user.js" << EOL
user_pref("general.useragent.override", "$RANDOM_UA");
user_pref("privacy.resistFingerprinting", true);
user_pref("privacy.trackingprotection.enabled", true);
user_pref("privacy.donottrackheader.enabled", true);
user_pref("dom.webdriver.enabled", false);
EOL
        
        firefox --profile "$profile_dir" --private-window &
        ;;
    *)
        echo "Usage: augment-browser {chrome|firefox}"
        exit 1
        ;;
esac
EOF
    
    chmod +x /usr/local/bin/augment-browser
    log "Browser fingerprint evasion script created"
}

# Main execution function
main() {
    log "Starting Augment Code Evasion Toolkit..."
    log "This will completely reset your device identity"
    
    check_root
    install_dependencies
    
    # Execute all identity reset functions
    reset_machine_id
    randomize_mac_addresses
    clear_browser_data
    clear_system_caches
    regenerate_ssh_keys
    create_browser_fingerprint_evasion
    
    log "✅ Augment Code Evasion Complete!"
    log "Your machine now appears as a completely new device"
    
    echo ""
    echo "📋 Next Steps:"
    echo "1. Restart your browser using: augment-browser chrome"
    echo "2. Restart VS Code"
    echo "3. Consider using VPN/proxy for IP masking"
    echo "4. Use incognito/private browsing mode"
    echo "5. Run this script again before each new augment session"
}

# Show usage
usage() {
    echo "Augment Code Evasion Toolkit"
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --full-reset    Complete device identity reset (default)"
    echo "  --mac-only      Only randomize MAC addresses"
    echo "  --browser-only  Only clear browser data"
    echo "  --system-only   Only clear system caches"
    echo "  --help          Show this help message"
}

# Parse command line arguments
case "$1" in
    --mac-only)
        check_root
        randomize_mac_addresses
        ;;
    --browser-only)
        clear_browser_data
        ;;
    --system-only)
        clear_system_caches
        ;;
    --help)
        usage
        ;;
    *)
        main
        ;;
esac

log "Script execution completed successfully!"
