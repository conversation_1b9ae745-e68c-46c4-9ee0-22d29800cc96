# Multi-stage Dockerfile for TeamSphere Application
# Stage 1: Build Angular Frontend
FROM node:18-alpine AS frontend-build

WORKDIR /app/frontend

# Copy package files
COPY org-chart/package*.json ./

# Install Angular CLI globally and dependencies
RUN npm install -g @angular/cli@16.2.16
RUN npm ci

# Copy source code
COPY org-chart/ ./

# Use Docker-specific environment configuration
RUN cp src/assets/env.docker.js src/assets/env.js

# Build the Angular application
RUN npm run build

# Stage 2: Build Spring Boot Backend
FROM maven:3.9-eclipse-temurin-21 AS backend-build

WORKDIR /app/backend

# Copy Maven files
COPY backend/capsAllocation/pom.xml ./
COPY backend/capsAllocation/mvnw ./
COPY backend/capsAllocation/mvnw.cmd ./
COPY backend/capsAllocation/.mvn ./.mvn

# Download dependencies
RUN mvn dependency:go-offline -B

# Copy source code
COPY backend/capsAllocation/src ./src

# Build the application
RUN mvn clean package -DskipTests

# Stage 3: Runtime Environment
FROM eclipse-temurin:21-jdk-jammy

# Install nginx and other utilities
RUN apt-get update && apt-get install -y \
    nginx \
    curl \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Create application directory
WORKDIR /app

# Copy built backend JAR
COPY --from=backend-build /app/backend/target/*.jar app.jar

# Copy built frontend
COPY --from=frontend-build /app/frontend/dist/org-chart /var/www/html

# Copy nginx configuration
COPY docker/nginx.conf /etc/nginx/sites-available/default

# Copy application configuration
COPY docker/application-docker.properties /app/application.properties

# Create uploads directory for profile pictures
RUN mkdir -p /app/uploads/profile-pics

# Create startup script
COPY docker/start.sh /app/start.sh
RUN chmod +x /app/start.sh

# Expose ports
EXPOSE 80 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# Start the application
CMD ["/app/start.sh"]
