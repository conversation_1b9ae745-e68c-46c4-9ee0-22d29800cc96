-- OAuth Migration Script for TeamSphere
-- This script adds OAuth support fields to the Users table

-- Add new columns for OAuth support
ALTER TABLE Users 
ADD COLUMN IF NOT EXISTS email VARCHAR(255) UNIQUE,
ADD COLUMN IF NOT EXISTS google_id VARCHAR(255) UNIQUE,
ADD COLUMN IF NOT EXISTS auth_provider VARCHAR(20) NOT NULL DEFAULT 'LOCAL',
ADD COLUMN IF NOT EXISTS first_name VARCHAR(255),
ADD COLUMN IF NOT EXISTS last_name VARCHAR(255);

-- Make password nullable for OAuth users
ALTER TABLE Users ALTER COLUMN password DROP NOT NULL;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON Users(email);
CREATE INDEX IF NOT EXISTS idx_users_google_id ON Users(google_id);
CREATE INDEX IF NOT EXISTS idx_users_auth_provider ON Users(auth_provider);

-- Add constraint to ensure auth_provider is valid (with error handling)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'chk_auth_provider'
        AND table_name = 'users'
    ) THEN
        ALTER TABLE Users ADD CONSTRAINT chk_auth_provider
        CHECK (auth_provider IN ('LOCAL', 'GOOGLE'));
    END IF;
END $$;

-- Update existing users to have LOCAL auth provider
UPDATE Users SET auth_provider = 'LOCAL' WHERE auth_provider IS NULL;
