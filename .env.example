# TeamSphere Environment Configuration
# Copy this file to .env and update the values

# Docker Configuration
DOCKER_USERNAME=your-dockerhub-username
IMAGE_NAME=teamsphere
TAG=latest

# Database Configuration
POSTGRES_PASSWORD=voyage
POSTGRES_DB=vbs_allocation_caps
POSTGRES_USER=postgres

# AWS SES Configuration (for email notifications)
AWS_SES_ACCESS_KEY=your-aws-access-key
AWS_SES_SECRET_KEY=your-aws-secret-key
AWS_SES_REGION=us-east-1
AWS_SES_FROM_EMAIL=<EMAIL>

# Google Cloud Configuration (for deployment)
GCE_PROJECT=your-project-id
GCE_INSTANCE_NAME=teamsphere-instance
GCE_ZONE=us-central1-a

# Application Configuration
JAVA_OPTS=-Xmx1g -Xms512m
SERVER_PORT=8080

# Backup Configuration
BACKUP_SHARE_EMAIL=<EMAIL>

# Security Configuration
JWT_SECRET=your-jwt-secret-key-here

# Google Sheets Configuration
GOOGLE_SHEETS_SPREADSHEET_ID=14USVFIoRJQzSgKA9_DI3_c_rOJy7G2W9txJRNRDCfAw
GOOGLE_SHEETS_SHEET_NAME=Response
