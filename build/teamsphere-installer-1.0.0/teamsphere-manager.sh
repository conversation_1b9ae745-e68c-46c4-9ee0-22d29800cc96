#!/bin/bash

# TeamSphere Management Script
# Version: 1.0
# Description: Manages TeamSphere application lifecycle

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SERVICE_NAME="teamsphere"
APP_HOME="/opt/teamsphere"

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if service exists
check_service() {
    if ! systemctl list-unit-files | grep -q "$SERVICE_NAME.service"; then
        log_error "TeamSphere service not found. Please run the installer first."
        exit 1
    fi
}

# Start the application
start_app() {
    log_info "Starting TeamSphere application..."
    sudo systemctl start $SERVICE_NAME
    sudo systemctl start nginx
    log_success "TeamSphere started successfully"
}

# Stop the application
stop_app() {
    log_info "Stopping TeamSphere application..."
    sudo systemctl stop $SERVICE_NAME
    log_success "TeamSphere stopped successfully"
}

# Restart the application
restart_app() {
    log_info "Restarting TeamSphere application..."
    sudo systemctl restart $SERVICE_NAME
    sudo systemctl restart nginx
    log_success "TeamSphere restarted successfully"
}

# Check application status
status_app() {
    log_info "Checking TeamSphere status..."
    echo ""
    echo "=== TeamSphere Service Status ==="
    sudo systemctl status $SERVICE_NAME --no-pager
    echo ""
    echo "=== Nginx Status ==="
    sudo systemctl status nginx --no-pager
    echo ""
    echo "=== PostgreSQL Status ==="
    sudo systemctl status postgresql --no-pager
}

# View application logs
logs_app() {
    log_info "Showing TeamSphere logs..."
    sudo journalctl -u $SERVICE_NAME -n 50 --no-pager
}

# Follow application logs
follow_logs() {
    log_info "Following TeamSphere logs (Ctrl+C to exit)..."
    sudo journalctl -u $SERVICE_NAME -f
}

# Enable auto-start
enable_app() {
    log_info "Enabling TeamSphere auto-start..."
    sudo systemctl enable $SERVICE_NAME
    sudo systemctl enable nginx
    sudo systemctl enable postgresql
    log_success "Auto-start enabled"
}

# Disable auto-start
disable_app() {
    log_info "Disabling TeamSphere auto-start..."
    sudo systemctl disable $SERVICE_NAME
    log_success "Auto-start disabled"
}

# Create backup
backup_app() {
    log_info "Creating TeamSphere backup..."
    BACKUP_DIR="/tmp/teamsphere-backup-$(date +%Y%m%d-%H%M%S)"
    mkdir -p $BACKUP_DIR

    # Backup application files
    sudo cp -r $APP_HOME $BACKUP_DIR/

    # Backup database
    sudo -u postgres pg_dump vbs_allocation_caps > $BACKUP_DIR/database.sql

    # Create archive
    tar -czf $BACKUP_DIR.tar.gz -C /tmp $(basename $BACKUP_DIR)
    rm -rf $BACKUP_DIR

    log_success "Backup created: $BACKUP_DIR.tar.gz"
}

# Launch browser
launch_browser() {
    log_info "Opening TeamSphere in your default browser..."
    local url="http://localhost"

    # Check if we're in a desktop environment
    if [ -n "$DISPLAY" ] || [ -n "$WAYLAND_DISPLAY" ]; then
        # Try different browser commands in order of preference
        if command -v xdg-open >/dev/null 2>&1; then
            log_info "Launching browser with xdg-open..."
            xdg-open "$url" >/dev/null 2>&1 &
        elif command -v gnome-open >/dev/null 2>&1; then
            log_info "Launching browser with gnome-open..."
            gnome-open "$url" >/dev/null 2>&1 &
        elif command -v firefox >/dev/null 2>&1; then
            log_info "Launching Firefox..."
            firefox "$url" >/dev/null 2>&1 &
        elif command -v google-chrome >/dev/null 2>&1; then
            log_info "Launching Google Chrome..."
            google-chrome "$url" >/dev/null 2>&1 &
        elif command -v chromium-browser >/dev/null 2>&1; then
            log_info "Launching Chromium..."
            chromium-browser "$url" >/dev/null 2>&1 &
        elif command -v opera >/dev/null 2>&1; then
            log_info "Launching Opera..."
            opera "$url" >/dev/null 2>&1 &
        else
            log_warning "No suitable browser found for auto-launch."
            log_info "Please manually open: $url"
            return 1
        fi

        log_success "Browser launched! TeamSphere should open shortly."
        log_info "If the browser doesn't open, manually navigate to: $url"
        return 0
    else
        log_info "No desktop environment detected (headless system)."
        log_info "Please manually open: $url in your browser"
        return 1
    fi
}

# Show usage
show_usage() {
    echo "TeamSphere Management Script"
    echo ""
    echo "Usage: $0 {start|stop|restart|status|logs|follow|enable|disable|backup|open}"
    echo ""
    echo "Commands:"
    echo "  start    - Start the TeamSphere application"
    echo "  stop     - Stop the TeamSphere application"
    echo "  restart  - Restart the TeamSphere application"
    echo "  status   - Show application status"
    echo "  logs     - Show recent application logs"
    echo "  follow   - Follow application logs in real-time"
    echo "  enable   - Enable auto-start on boot"
    echo "  disable  - Disable auto-start on boot"
    echo "  backup   - Create a backup of the application"
    echo "  open     - Open TeamSphere in your default browser"
    echo ""
}

# Main function
main() {
    case "${1:-}" in
        start)
            check_service
            start_app
            ;;
        stop)
            check_service
            stop_app
            ;;
        restart)
            check_service
            restart_app
            ;;
        status)
            check_service
            status_app
            ;;
        logs)
            check_service
            logs_app
            ;;
        follow)
            check_service
            follow_logs
            ;;
        enable)
            check_service
            enable_app
            ;;
        disable)
            check_service
            disable_app
            ;;
        backup)
            check_service
            backup_app
            ;;
        open)
            launch_browser
            ;;
        *)
            show_usage
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
