#!/bin/bash

# TeamSphere Uninstaller
# Version: 1.0
# Description: Removes TeamSphere application from the system

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="teamsphere"
APP_USER="teamsphere"
APP_HOME="/opt/teamsphere"
SERVICE_NAME="teamsphere"
DB_NAME="vbs_allocation_caps"
DB_USER="teamsphere"

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Confirm uninstallation
confirm_uninstall() {
    echo ""
    log_warning "This will completely remove TeamSphere from your system!"
    echo ""
    echo "What would you like to remove?"
    echo "1) Application only (keep database and user data)"
    echo "2) Application + Database (keep system dependencies)"
    echo "3) Everything (application, database, and dependencies)"
    echo "4) Cancel"
    echo ""
    read -p "Enter your choice (1-4): " choice

    case $choice in
        1) REMOVE_LEVEL="app" ;;
        2) REMOVE_LEVEL="app_db" ;;
        3) REMOVE_LEVEL="all" ;;
        4) log_info "Uninstallation cancelled"; exit 0 ;;
        *) log_error "Invalid choice"; exit 1 ;;
    esac
}

# Stop services
stop_services() {
    log_info "Stopping services..."

    sudo systemctl stop $SERVICE_NAME 2>/dev/null || true
    sudo systemctl disable $SERVICE_NAME 2>/dev/null || true

    log_success "Services stopped"
}

# Remove application files
remove_application() {
    log_info "Removing application files..."

    # Remove systemd service
    sudo rm -f /etc/systemd/system/$SERVICE_NAME.service
    sudo systemctl daemon-reload

    # Remove application directory
    sudo rm -rf $APP_HOME
    sudo rm -rf /var/log/$APP_NAME
    sudo rm -rf /etc/$APP_NAME

    # Remove nginx configuration
    sudo rm -f /etc/nginx/sites-available/$APP_NAME
    sudo rm -f /etc/nginx/sites-enabled/$APP_NAME

    # Restore default nginx site
    sudo ln -sf /etc/nginx/sites-available/default /etc/nginx/sites-enabled/ 2>/dev/null || true

    # Remove application user
    sudo userdel $APP_USER 2>/dev/null || true

    log_success "Application files removed"
}

# Remove database
remove_database() {
    log_info "Removing database..."

    sudo -u postgres dropdb $DB_NAME 2>/dev/null || true
    sudo -u postgres dropuser $DB_USER 2>/dev/null || true

    log_success "Database removed"
}

# Remove dependencies
remove_dependencies() {
    log_info "Removing system dependencies..."

    # Detect distribution
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        DISTRO=$ID
    else
        log_warning "Cannot detect distribution, skipping dependency removal"
        return
    fi

    case $DISTRO in
        ubuntu|debian)
            sudo apt-get remove --purge -y openjdk-17-jdk nodejs postgresql postgresql-contrib nginx
            sudo apt-get autoremove -y
            ;;
        centos|rhel|fedora)
            if command -v dnf &> /dev/null; then
                sudo dnf remove -y java-17-openjdk java-17-openjdk-devel nodejs npm postgresql postgresql-server postgresql-contrib nginx
            else
                sudo yum remove -y java-17-openjdk java-17-openjdk-devel nodejs npm postgresql postgresql-server postgresql-contrib nginx
            fi
            ;;
    esac

    log_success "Dependencies removed"
}

# Main uninstallation function
main() {
    log_info "Starting TeamSphere uninstallation..."

    confirm_uninstall

    stop_services
    remove_application

    if [ "$REMOVE_LEVEL" = "app_db" ] || [ "$REMOVE_LEVEL" = "all" ]; then
        remove_database
    fi

    if [ "$REMOVE_LEVEL" = "all" ]; then
        remove_dependencies
    fi

    log_success "TeamSphere uninstallation completed!"

    if [ "$REMOVE_LEVEL" = "app" ]; then
        log_info "Database and user data preserved"
    elif [ "$REMOVE_LEVEL" = "app_db" ]; then
        log_info "System dependencies preserved (Java, Node.js, PostgreSQL, Nginx)"
    fi
}

# Run main function
main "$@"
