#!/bin/bash

# TeamSphere Linux Installer
# Version: 1.0
# Description: Installs TeamSphere application on Linux systems

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="teamsphere"
APP_USER="teamsphere"
APP_HOME="/opt/teamsphere"
SERVICE_NAME="teamsphere"
DB_NAME="vbs_allocation_caps"
DB_USER="teamsphere"
DB_PASSWORD=""
FRONTEND_PORT="80"
BACKEND_PORT="8080"
SERVER_IP=""
SYSTEM_TYPE=""

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "This script should not be run as root. Please run as a regular user with sudo privileges."
        exit 1
    fi
}

# Check if user has sudo privileges
check_sudo() {
    if ! sudo -n true 2>/dev/null; then
        log_error "This script requires sudo privileges. Please ensure your user has sudo access."
        exit 1
    fi
}

# Detect system type and configure IP
detect_system_type() {
    log_info "Detecting system type for CORS configuration..."

    echo ""
    echo "Please select your system type:"
    echo "1) Regular Linux system (localhost)"
    echo "2) Chromebook with Linux (Penguin/WSL-like environment)"
    echo "3) WSL (Windows Subsystem for Linux)"
    echo "4) Custom IP address"
    echo ""

    while true; do
        read -p "Enter your choice (1-4): " choice
        case $choice in
            1)
                SYSTEM_TYPE="regular"
                SERVER_IP="localhost"
                log_info "Selected: Regular Linux system"
                break
                ;;
            2)
                SYSTEM_TYPE="chromebook"
                # Get the IP address using hostname -I for Chromebook/Penguin
                SERVER_IP=$(hostname -I | awk '{print $1}')
                if [ -z "$SERVER_IP" ]; then
                    log_warning "Could not detect IP automatically. Please enter manually."
                    read -p "Enter your IP address: " SERVER_IP
                fi
                log_info "Selected: Chromebook/Penguin environment with IP: $SERVER_IP"
                break
                ;;
            3)
                SYSTEM_TYPE="wsl"
                # Get the IP address using hostname -I for WSL
                SERVER_IP=$(hostname -I | awk '{print $1}')
                if [ -z "$SERVER_IP" ]; then
                    log_warning "Could not detect IP automatically. Please enter manually."
                    read -p "Enter your IP address: " SERVER_IP
                fi
                log_info "Selected: WSL environment with IP: $SERVER_IP"
                break
                ;;
            4)
                SYSTEM_TYPE="custom"
                read -p "Enter your custom IP address: " SERVER_IP
                log_info "Selected: Custom IP address: $SERVER_IP"
                break
                ;;
            *)
                log_error "Invalid choice. Please enter 1, 2, 3, or 4."
                ;;
        esac
    done

    log_success "System type configured: $SYSTEM_TYPE with IP: $SERVER_IP"
}

# Detect Linux distribution
detect_distro() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        DISTRO=$ID
        VERSION=$VERSION_ID
    else
        log_error "Cannot detect Linux distribution"
        exit 1
    fi

    log_info "Detected distribution: $DISTRO $VERSION"
}

# Check system requirements
check_requirements() {
    log_info "Checking system requirements..."

    # Check available memory (minimum 2GB)
    MEMORY_KB=$(grep MemTotal /proc/meminfo | awk '{print $2}')
    MEMORY_GB=$((MEMORY_KB / 1024 / 1024))

    if [ $MEMORY_GB -lt 2 ]; then
        log_warning "System has less than 2GB RAM. Application may run slowly."
    fi

    # Check available disk space (minimum 5GB)
    AVAILABLE_SPACE=$(df / | awk 'NR==2 {print $4}')
    AVAILABLE_GB=$((AVAILABLE_SPACE / 1024 / 1024))

    if [ $AVAILABLE_GB -lt 5 ]; then
        log_error "Insufficient disk space. At least 5GB required."
        exit 1
    fi

    log_success "System requirements check passed"
}

# Install system dependencies
install_dependencies() {
    log_info "Installing system dependencies..."

    case $DISTRO in
        ubuntu|debian)
            sudo apt-get update
            sudo apt-get install -y curl wget gnupg2 software-properties-common apt-transport-https ca-certificates
            sudo apt-get install -y postgresql postgresql-contrib nginx
            ;;
        centos|rhel|fedora)
            if command -v dnf &> /dev/null; then
                sudo dnf update -y
                sudo dnf install -y curl wget gnupg2 ca-certificates
                sudo dnf install -y postgresql postgresql-server postgresql-contrib nginx
            else
                sudo yum update -y
                sudo yum install -y curl wget gnupg2 ca-certificates
                sudo yum install -y postgresql postgresql-server postgresql-contrib nginx
            fi
            ;;
        *)
            log_error "Unsupported distribution: $DISTRO"
            exit 1
            ;;
    esac

    log_success "System dependencies installed"
}

# Install Java 17
install_java() {
    log_info "Installing Java 17..."

    if command -v java &> /dev/null; then
        JAVA_VERSION=$(java -version 2>&1 | head -n1 | awk -F '"' '{print $2}' | awk -F '.' '{print $1}')
        if [ "$JAVA_VERSION" -ge 17 ]; then
            log_success "Java $JAVA_VERSION is already installed"
            return
        fi
    fi

    case $DISTRO in
        ubuntu|debian)
            sudo apt-get install -y openjdk-17-jdk
            ;;
        centos|rhel|fedora)
            if command -v dnf &> /dev/null; then
                sudo dnf install -y java-17-openjdk java-17-openjdk-devel
            else
                sudo yum install -y java-17-openjdk java-17-openjdk-devel
            fi
            ;;
    esac

    # Set JAVA_HOME
    JAVA_HOME=$(readlink -f /usr/bin/java | sed "s:bin/java::")
    echo "export JAVA_HOME=$JAVA_HOME" | sudo tee -a /etc/environment
    export JAVA_HOME=$JAVA_HOME

    log_success "Java 17 installed successfully"
}

# Note: Node.js installation removed since we're using pre-built frontend artifacts

# Create application user
create_app_user() {
    log_info "Creating application user..."

    if id "$APP_USER" &>/dev/null; then
        log_success "User $APP_USER already exists"
    else
        sudo useradd -r -s /bin/false -d $APP_HOME $APP_USER
        log_success "User $APP_USER created"
    fi
}

# Create application directories
create_directories() {
    log_info "Creating application directories..."

    sudo mkdir -p $APP_HOME/{bin,config,logs,data,frontend}
    sudo mkdir -p /var/log/$APP_NAME
    sudo mkdir -p /etc/$APP_NAME

    sudo chown -R $APP_USER:$APP_USER $APP_HOME
    sudo chown -R $APP_USER:$APP_USER /var/log/$APP_NAME

    log_success "Application directories created"
}

# Create additional log directories for the application
create_log_directories() {
    log_info "Creating additional log directories..."

    # Create the specific log directories that the application expects
    sudo mkdir -p $APP_HOME/TeamSphereErrorLogs
    sudo mkdir -p $APP_HOME/TeamSphereDebugLogs

    # Set proper ownership
    sudo chown -R $APP_USER:$APP_USER $APP_HOME/TeamSphereErrorLogs
    sudo chown -R $APP_USER:$APP_USER $APP_HOME/TeamSphereDebugLogs

    # Set proper permissions
    sudo chmod 755 $APP_HOME/TeamSphereErrorLogs
    sudo chmod 755 $APP_HOME/TeamSphereDebugLogs

    log_success "Additional log directories created"
}

# Setup PostgreSQL database
setup_database() {
    log_info "Setting up PostgreSQL database..."

    # Initialize PostgreSQL if needed
    if [ "$DISTRO" = "centos" ] || [ "$DISTRO" = "rhel" ] || [ "$DISTRO" = "fedora" ]; then
        if [ ! -f /var/lib/pgsql/data/postgresql.conf ]; then
            sudo postgresql-setup initdb
        fi
    fi

    # Start and enable PostgreSQL
    sudo systemctl start postgresql
    sudo systemctl enable postgresql

    # Set postgres user password to 'voyage'
    log_info "Setting postgres user password..."
    sudo -u postgres psql -c "ALTER USER postgres WITH PASSWORD 'voyage';" 2>/dev/null || true

    # Set default password if not provided
    if [ -z "$DB_PASSWORD" ]; then
        DB_PASSWORD="voyage"
        log_info "Using default database password: $DB_PASSWORD"
    fi

    # Create database and user
    sudo -u postgres psql -c "CREATE DATABASE $DB_NAME;" 2>/dev/null || true
    sudo -u postgres psql -c "CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';" 2>/dev/null || true
    sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;" 2>/dev/null || true
    sudo -u postgres psql -c "ALTER DATABASE $DB_NAME OWNER TO $DB_USER;" 2>/dev/null || true
    sudo -u postgres psql -c "ALTER USER $DB_USER CREATEDB;" 2>/dev/null || true

    # Grant permissions on existing tables and sequences (if any)
    sudo -u postgres psql -d $DB_NAME -c "GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO $DB_USER;" 2>/dev/null || true
    sudo -u postgres psql -d $DB_NAME -c "GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO $DB_USER;" 2>/dev/null || true
    sudo -u postgres psql -d $DB_NAME -c "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO $DB_USER;" 2>/dev/null || true

    log_success "PostgreSQL database setup completed"
}

# Test database connection and fix any issues
test_database_connection() {
    log_info "Testing database connection..."

    # Test the connection with the created credentials
    if PGPASSWORD="$DB_PASSWORD" psql -h localhost -U $DB_USER -d $DB_NAME -c "SELECT 1;" >/dev/null 2>&1; then
        log_success "Database connection test passed"
    else
        log_warning "Database connection test failed, attempting to fix permissions..."

        # Reset password to ensure it's correct
        sudo -u postgres psql -c "ALTER USER $DB_USER PASSWORD '$DB_PASSWORD';" 2>/dev/null || true

        # Re-grant all permissions
        sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;" 2>/dev/null || true
        sudo -u postgres psql -c "ALTER DATABASE $DB_NAME OWNER TO $DB_USER;" 2>/dev/null || true
        sudo -u postgres psql -d $DB_NAME -c "GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO $DB_USER;" 2>/dev/null || true
        sudo -u postgres psql -d $DB_NAME -c "GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO $DB_USER;" 2>/dev/null || true

        # Test again
        if PGPASSWORD="$DB_PASSWORD" psql -h localhost -U $DB_USER -d $DB_NAME -c "SELECT 1;" >/dev/null 2>&1; then
            log_success "Database connection fixed and working"
        else
            log_error "Database connection still failing. Please check PostgreSQL configuration."
            exit 1
        fi
    fi
}

# Restore database from dump file
restore_database() {
    log_info "Restoring database from dump file..."

    # Check if database dump file exists
    if [ ! -f "database/db.dump" ]; then
        log_warning "Database dump file not found. Skipping database restoration."
        log_info "Database will be initialized empty and populated by the application."
        return 0
    fi

    log_info "Found database dump file. Proceeding with restoration..."

    # First, drop and recreate the database to ensure clean restoration
    log_info "Preparing database for restoration..."
    sudo -u postgres psql -c "DROP DATABASE IF EXISTS $DB_NAME;" 2>/dev/null || true
    sudo -u postgres psql -c "CREATE DATABASE $DB_NAME;" 2>/dev/null || true
    sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;" 2>/dev/null || true
    sudo -u postgres psql -c "ALTER DATABASE $DB_NAME OWNER TO $DB_USER;" 2>/dev/null || true

    # Restore the database using pg_restore
    log_info "Restoring database from dump file..."
    if PGPASSWORD="voyage" pg_restore -U postgres -d $DB_NAME -h localhost -p 5432 --clean --if-exists database/db.dump 2>/dev/null; then
        log_success "Database restored successfully from dump file"
    else
        log_warning "pg_restore failed, trying alternative restoration method..."

        # Try using psql if pg_restore fails (in case it's a SQL dump)
        if PGPASSWORD="voyage" psql -U postgres -d $DB_NAME -h localhost -p 5432 -f database/db.dump >/dev/null 2>&1; then
            log_success "Database restored successfully using psql"
        else
            log_warning "Database restoration failed. The application will initialize with an empty database."
            log_info "You can manually restore the database later using:"
            log_info "  pg_restore -U postgres -d $DB_NAME -h localhost -p 5432 --clean --if-exists database/db.dump"
            log_info "  or"
            log_info "  psql -U postgres -d $DB_NAME -h localhost -p 5432 -f database/db.dump"
        fi
    fi

    # Re-grant permissions after restoration
    log_info "Setting up database permissions after restoration..."
    sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;" 2>/dev/null || true
    sudo -u postgres psql -c "ALTER DATABASE $DB_NAME OWNER TO $DB_USER;" 2>/dev/null || true
    sudo -u postgres psql -d $DB_NAME -c "GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO $DB_USER;" 2>/dev/null || true
    sudo -u postgres psql -d $DB_NAME -c "GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO $DB_USER;" 2>/dev/null || true
    sudo -u postgres psql -d $DB_NAME -c "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO $DB_USER;" 2>/dev/null || true

    log_success "Database restoration process completed"
}

# Deploy frontend using pre-built artifacts
deploy_frontend() {
    log_info "Deploying frontend..."

    if [ ! -d "frontend" ]; then
        log_error "Frontend build artifacts not found"
        exit 1
    fi

    # Copy built files to application directory
    sudo cp -r frontend/* $APP_HOME/frontend/
    sudo chown -R $APP_USER:$APP_USER $APP_HOME/frontend/

    log_success "Frontend deployed successfully"
}

# Deploy backend using pre-built artifacts
deploy_backend() {
    log_info "Deploying backend..."

    if [ ! -f "backend/teamsphere.jar" ]; then
        log_error "Backend JAR file not found"
        exit 1
    fi

    # Copy pre-built JAR file to application directory
    sudo cp backend/teamsphere.jar $APP_HOME/bin/teamsphere.jar
    sudo chown $APP_USER:$APP_USER $APP_HOME/bin/teamsphere.jar

    # Copy configuration files if they exist
    if [ -d "backend/config" ]; then
        sudo cp -r backend/config/* $APP_HOME/config/ 2>/dev/null || true
        sudo chown -R $APP_USER:$APP_USER $APP_HOME/config/
    fi

    log_success "Backend deployed successfully"
}

# Create application configuration
create_config() {
    log_info "Creating application configuration..."

    # Create application.properties
    sudo tee $APP_HOME/config/application.properties > /dev/null <<APPEOF
# Database Configuration
spring.datasource.url=*****************************************
spring.datasource.username=$DB_USER
spring.datasource.password=$DB_PASSWORD
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.default_schema=public

# Server Configuration
server.port=$BACKEND_PORT

# File Upload Configuration
spring.web.resources.static-locations=file:$APP_HOME/data/uploads/
spring.servlet.multipart.max-file-size=1MB
spring.servlet.multipart.max-request-size=1MB

# Logging Configuration
logging.level.root=INFO
logging.level.org.springframework=INFO
logging.level.com.vbs.capsAllocation=DEBUG
logging.file.name=$APP_HOME/logs/teamsphere.log
logging.file.max-size=10MB
logging.file.max-history=30

# Mail Configuration (update with your SMTP settings)
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=your-app-password
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# Google Sheets Configuration (update with your credentials)
google.sheets.spreadsheetId=your-spreadsheet-id
google.sheets.credentials=classpath:ops-excellence-credentials.json
google.sheets.sheetName=Response
APPEOF

    # Create environment configuration for frontend based on system type
    sudo tee $APP_HOME/frontend/assets/env.js > /dev/null <<ENVEOF
window.env = {
    SERVER_IP: "$SERVER_IP"
};
ENVEOF

    sudo chown -R $APP_USER:$APP_USER $APP_HOME/config/
    sudo chown $APP_USER:$APP_USER $APP_HOME/frontend/assets/env.js

    log_success "Application configuration created"
}

# Note: CORS configuration update removed since we're using pre-built JAR files
# The CORS configuration is already compiled into the JAR file

# Create systemd service
create_service() {
    log_info "Creating systemd service..."

    sudo tee /etc/systemd/system/$SERVICE_NAME.service > /dev/null <<SERVICEEOF
[Unit]
Description=TeamSphere Application
After=network.target postgresql.service
Requires=postgresql.service

[Service]
Type=simple
User=$APP_USER
Group=$APP_USER
WorkingDirectory=$APP_HOME
ExecStart=/usr/bin/java -jar $APP_HOME/bin/teamsphere.jar --spring.config.location=file:$APP_HOME/config/application.properties
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$SERVICE_NAME

# Environment variables
Environment=JAVA_HOME=$JAVA_HOME
Environment=SPRING_PROFILES_ACTIVE=production

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=$APP_HOME/logs $APP_HOME/data /var/log/$APP_NAME $APP_HOME/TeamSphereErrorLogs $APP_HOME/TeamSphereDebugLogs

[Install]
WantedBy=multi-user.target
SERVICEEOF

    sudo systemctl daemon-reload
    sudo systemctl enable $SERVICE_NAME

    log_success "Systemd service created"
}

# Configure Nginx
configure_nginx() {
    log_info "Configuring Nginx for system type: $SYSTEM_TYPE..."

    # Backup original configuration
    sudo cp /etc/nginx/sites-available/default /etc/nginx/sites-available/default.backup 2>/dev/null || true

    # Determine server_name based on system type
    if [ "$SYSTEM_TYPE" = "regular" ]; then
        SERVER_NAME="localhost"
    else
        SERVER_NAME="localhost $SERVER_IP"
    fi

    # Create TeamSphere Nginx configuration
    sudo tee /etc/nginx/sites-available/$APP_NAME > /dev/null <<NGINXEOF
server {
    listen $FRONTEND_PORT;
    server_name $SERVER_NAME;

    # Frontend static files
    location / {
        root $APP_HOME/frontend;
        try_files \$uri \$uri/ /index.html;
        index index.html;

        # Add CORS headers for frontend
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
    }

    # Backend API proxy
    location /api/ {
        proxy_pass http://localhost:$BACKEND_PORT/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;

        # CORS headers for API
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;

        if (\$request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
        }
    }

    # Static assets
    location /assets/ {
        root $APP_HOME/frontend;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Logs
    access_log /var/log/nginx/$APP_NAME.access.log;
    error_log /var/log/nginx/$APP_NAME.error.log;
}
NGINXEOF

    # Enable the site
    sudo ln -sf /etc/nginx/sites-available/$APP_NAME /etc/nginx/sites-enabled/
    sudo rm -f /etc/nginx/sites-enabled/default

    # Test Nginx configuration
    sudo nginx -t

    # Start and enable Nginx
    sudo systemctl start nginx
    sudo systemctl enable nginx

    log_success "Nginx configured successfully"
}

# Start services and verify installation
start_and_verify_services() {
    log_info "Starting services and verifying installation..."

    # Start the TeamSphere service
    sudo systemctl start $SERVICE_NAME

    # Wait a moment for the service to start
    sleep 5

    # Check if the service is running
    if sudo systemctl is-active --quiet $SERVICE_NAME; then
        log_success "TeamSphere service started successfully"
    else
        log_warning "TeamSphere service may not have started properly"
        log_info "Checking service logs..."
        sudo journalctl -u $SERVICE_NAME -n 10 --no-pager
    fi

    # Ensure Nginx is running
    sudo systemctl restart nginx

    # Test if the frontend is accessible
    if curl -s http://localhost/ | grep -q "TeamSphere"; then
        log_success "Frontend is accessible"
    else
        log_warning "Frontend may not be properly configured"
    fi

    log_success "Service verification completed"
}

# Create default admin user
create_default_admin_user() {
    log_info "Creating default admin user..."

    # Wait for the application to be fully ready
    local max_attempts=30
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        log_info "Waiting for application to be ready (attempt $attempt/$max_attempts)..."

        # Check if the application is responding
        if curl -s http://localhost:$BACKEND_PORT/actuator/health >/dev/null 2>&1; then
            log_success "Application is ready"
            break
        fi

        if [ $attempt -eq $max_attempts ]; then
            log_warning "Application may not be fully ready, but proceeding with user creation..."
            break
        fi

        sleep 10
        attempt=$((attempt + 1))
    done

    # Create the default admin user using the signup endpoint
    log_info "Creating admin user with username 'admin' and role 'ADMIN_OPS_MANAGER'..."

    local signup_response
    signup_response=$(curl -s -w "%{http_code}" -o /tmp/signup_response.txt \
        -X POST "http://localhost:$BACKEND_PORT/admin/register?username=admin&password=adminpassword&role=ADMIN_OPS_MANAGER" \
        -H "Content-Type: application/json" \
        2>/dev/null)

    local http_code="${signup_response: -3}"
    local response_body=$(cat /tmp/signup_response.txt 2>/dev/null || echo "No response")

    if [ "$http_code" = "201" ] || [ "$http_code" = "200" ]; then
        log_success "Default admin user created successfully"
        log_info "Response: $response_body"
        log_info "Admin credentials:"
        log_info "  Username: admin"
        log_info "  Password: adminpassword"
        log_info "  Role: ADMIN_OPS_MANAGER"
        log_warning "Please change the default password after first login!"
    else
        log_warning "Failed to create admin user (HTTP $http_code)"
        log_info "Response: $response_body"
        log_info "You can manually create an admin user later using:"
        log_info "  curl -X POST 'http://localhost:$BACKEND_PORT/admin/register?username=admin&password=adminpassword&role=ADMIN_OPS_MANAGER'"
    fi

    # Clean up temporary file
    rm -f /tmp/signup_response.txt
}

# Main installation function
main() {
    log_info "Starting TeamSphere installation..."

    check_root
    check_sudo
    detect_system_type
    detect_distro
    check_requirements
    install_dependencies
    install_java
    create_app_user
    create_directories
    create_log_directories
    setup_database
    test_database_connection
    restore_database
    deploy_frontend
    deploy_backend
    create_config
    create_service
    configure_nginx
    start_and_verify_services
    create_default_admin_user

    log_success "TeamSphere installation completed successfully!"
    log_info ""
    log_info "=== IMPORTANT CREDENTIALS ==="
    log_info "Database password: $DB_PASSWORD"
    log_info "PostgreSQL admin password: voyage"
    log_info "Default Admin User:"
    log_info "  Username: admin"
    log_info "  Password: adminpassword"
    log_info "  Role: ADMIN_OPS_MANAGER"
    log_warning "SECURITY: Please change the default admin password after first login!"
    log_info "Please save these credentials securely."
    log_info ""
    log_info "=== DATABASE INFORMATION ==="
    log_info "Database has been restored from the provided dump file (db.dump)."
    log_info "Database Name: $DB_NAME"
    log_info "Database User: $DB_USER"
    log_info "If you need to manually restore the database later, use:"
    log_info "  pg_restore -U postgres -d $DB_NAME -h localhost -p 5432 --clean --if-exists /path/to/db.dump"
    log_info ""
    log_info "=== SYSTEM CONFIGURATION ==="
    log_info "System Type: $SYSTEM_TYPE"
    log_info "Server IP: $SERVER_IP"
    log_info ""
    log_info "=== CORS CONFIGURATION ==="
    log_info "The application is pre-configured to accept requests from:"
    log_info "  - http://localhost:4200 (Angular dev server)"
    log_info "  - http://localhost:8080 (Backend default port)"
    log_info "  - http://localhost:80 (Production frontend)"
    if [ "$SYSTEM_TYPE" != "regular" ]; then
        log_info "  - http://$SERVER_IP (System IP)"
        log_info "  - http://$SERVER_IP:4200 (Angular dev server on system IP)"
        log_info "  - http://$SERVER_IP:8080 (Backend on system IP)"
        log_info "  - http://$SERVER_IP:80 (Production frontend on system IP)"
    fi
    log_info "CORS configuration is built into the application JAR file."
    log_info ""
    log_info "=== APPLICATION ACCESS ==="
    log_info "Services have been started automatically."
    if [ "$SYSTEM_TYPE" = "regular" ]; then
        log_info "Web Interface: http://localhost:$FRONTEND_PORT"
        log_info "Backend API: http://localhost:$BACKEND_PORT"
    else
        log_info "Web Interface: http://$SERVER_IP:$FRONTEND_PORT"
        log_info "Alternative: http://localhost:$FRONTEND_PORT"
        log_info "Backend API: http://$SERVER_IP:$BACKEND_PORT"
        log_info "Alternative: http://localhost:$BACKEND_PORT"
    fi
    log_info ""
    log_info "=== SERVICE MANAGEMENT ==="
    log_info "Check status: sudo systemctl status $SERVICE_NAME"
    log_info "View logs: sudo journalctl -u $SERVICE_NAME -f"
    log_info "Restart: sudo systemctl restart $SERVICE_NAME"
    log_info ""
    log_info "=== TROUBLESHOOTING ==="
    log_info "If you encounter any issues, check the logs and ensure all services are running:"
    log_info "  sudo systemctl status $SERVICE_NAME nginx postgresql"
    log_info ""
    log_info "You can now log in to the application using the admin credentials above."

    # Auto-launch browser
    log_info ""
    log_info "=== LAUNCHING APPLICATION ==="
    log_info "Opening TeamSphere in your default browser..."

    # Function to detect and launch browser
    launch_browser() {
        # Determine the correct URL based on system type
        if [ "$SYSTEM_TYPE" = "regular" ]; then
            local url="http://localhost:\$FRONTEND_PORT"
        else
            local url="http://\$SERVER_IP:\$FRONTEND_PORT"
        fi

        # Check if we're in a desktop environment
        if [ -n "\$DISPLAY" ] || [ -n "\$WAYLAND_DISPLAY" ]; then
            # Try different browser commands in order of preference
            if command -v xdg-open >/dev/null 2>&1; then
                log_info "Launching browser with xdg-open..."
                xdg-open "\$url" >/dev/null 2>&1 &
            elif command -v gnome-open >/dev/null 2>&1; then
                log_info "Launching browser with gnome-open..."
                gnome-open "\$url" >/dev/null 2>&1 &
            elif command -v firefox >/dev/null 2>&1; then
                log_info "Launching Firefox..."
                firefox "\$url" >/dev/null 2>&1 &
            elif command -v google-chrome >/dev/null 2>&1; then
                log_info "Launching Google Chrome..."
                google-chrome "\$url" >/dev/null 2>&1 &
            elif command -v chromium-browser >/dev/null 2>&1; then
                log_info "Launching Chromium..."
                chromium-browser "\$url" >/dev/null 2>&1 &
            elif command -v opera >/dev/null 2>&1; then
                log_info "Launching Opera..."
                opera "\$url" >/dev/null 2>&1 &
            else
                log_warning "No suitable browser found for auto-launch."
                log_info "Please manually open: \$url"
                return 1
            fi

            log_success "Browser launched! TeamSphere should open shortly."
            log_info "If the browser doesn't open, manually navigate to: \$url"
            return 0
        else
            log_info "No desktop environment detected (headless system)."
            log_info "Please manually open: \$url in your browser"
            return 1
        fi
    }

    # Launch browser
    launch_browser

    log_info ""
    log_info "=== QUICK START ==="
    log_info "1. Login with username: admin, password: adminpassword"
    log_info "2. Change the default password in user settings"
    log_info "3. Start using TeamSphere!"
}

# Run main function
main "$@"
