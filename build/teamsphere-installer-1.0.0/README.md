# TeamSphere Linux Installer

This installer package allows you to install and run TeamSphere application locally on any Linux system.

## Overview

TeamSphere is a comprehensive team management application consisting of:
- **Frontend**: Angular 16 application for the user interface
- **Backend**: Spring Boot application providing REST APIs
- **Database**: PostgreSQL for data storage with pre-populated data from included dump file
- **Web Server**: Nginx for serving frontend and proxying backend requests

### Database Restoration
This installer includes a database dump file (db.dump) that will be automatically restored during installation, providing:
- Pre-configured database schema
- Sample data and initial configurations
- Ready-to-use application state

## System Requirements

### Minimum Requirements
- **OS**: Ubuntu 18.04+, Debian 10+, CentOS 7+, RHEL 7+, Fedora 30+
- **RAM**: 2GB (4GB recommended)
- **Disk Space**: 5GB free space
- **Network**: Internet connection for downloading dependencies

### Supported Distributions
- Ubuntu 18.04, 20.04, 22.04, 24.04
- Debian 10, 11, 12
- CentOS 7, 8
- RHEL 7, 8, 9
- <PERSON><PERSON> 30+

## Installation

### Prerequisites
1. A regular user account with sudo privileges
2. Internet connection for downloading dependencies

### Quick Installation

1. **Download the installer files** to your system
2. **Make the installer executable**:
   ```bash
   chmod +x teamsphere-installer.sh
   ```

3. **Run the installer**:
   ```bash
   ./teamsphere-installer.sh
   ```

4. **Select your system type** when prompted:
   - **Regular Linux system**: For standard Linux installations
   - **Chromebook with Linux (Penguin)**: For Chromebook users running Linux
   - **WSL (Windows Subsystem for Linux)**: For Windows users with WSL
   - **Custom IP address**: For custom network configurations

5. **Follow the installation process** - the installer will:
   - Detect your system type and configure CORS accordingly
   - Check system requirements
   - Install dependencies (Java 17, Node.js, PostgreSQL, Nginx)
   - Create application user and directories
   - Build and deploy the application
   - Configure services with proper IP settings
   - Set up the database

6. **Start the application**:
   ```bash
   sudo systemctl start teamsphere
   ```

### What Gets Installed

The installer will install and configure:

- **Java 17 OpenJDK** - Required for the Spring Boot backend
- **Node.js 18 LTS** - Required for building the Angular frontend
- **PostgreSQL** - Database server with restored data from included dump file
- **Nginx** - Web server and reverse proxy
- **TeamSphere Application** - Installed in `/opt/teamsphere`
- **Systemd Service** - For managing the application lifecycle
- **Database Restoration** - Automatically restores database from included db.dump file

## Usage

### Managing the Application

Use the provided management script for common operations:

```bash
# Make the manager script executable
chmod +x teamsphere-manager.sh

# Start the application
./teamsphere-manager.sh start

# Stop the application
./teamsphere-manager.sh stop

# Restart the application
./teamsphere-manager.sh restart

# Check application status
./teamsphere-manager.sh status

# View logs
./teamsphere-manager.sh logs

# Follow logs in real-time
./teamsphere-manager.sh follow

# Enable auto-start on boot
./teamsphere-manager.sh enable

# Create a backup
./teamsphere-manager.sh backup
```

### Accessing the Application

Once installed and started:

- **Web Interface**: http://localhost (port 80)
- **Backend API**: http://localhost:8080
- **Database**: localhost:5432

## Uninstallation

To completely remove TeamSphere:

1. **Make the uninstaller executable**:
   ```bash
   chmod +x teamsphere-uninstaller.sh
   ```

2. **Run the uninstaller**:
   ```bash
   ./teamsphere-uninstaller.sh
   ```

3. **Follow the prompts** to choose what to remove:
   - Application files and configuration
   - Database and user data
   - System dependencies

## Support

For issues and support:
1. Check the troubleshooting section above
2. Review application logs
3. Ensure all services are running
4. Verify configuration files

## License

This installer is provided as-is for deploying the TeamSphere application locally.
