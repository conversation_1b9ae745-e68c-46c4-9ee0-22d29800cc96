# TeamSphere Quick Installation Guide

## Prerequisites
- Linux system (Ubuntu, Debian, CentOS, RHEL, Fedora)
- User account with sudo privileges
- Internet connection

## Installation Steps

1. **Extract the package** (if downloaded as archive):
   ```bash
   tar -xzf teamsphere-installer-*.tar.gz
   cd teamsphere-installer-*
   ```

2. **Run the installer**:
   ```bash
   ./teamsphere-installer.sh
   ```

3. **Select your system type** when prompted:
   - Regular Linux system (localhost)
   - Chromebook with Linux (Penguin/WSL-like)
   - WSL (Windows Subsystem for Linux)
   - Custom IP address

4. **Start the application**:
   ```bash
   sudo systemctl start teamsphere
   ```

4. **Access the application**:
   - For regular Linux: http://localhost
   - For Chromebook/WSL: http://[your-detected-ip]
   - The installer will display the correct URL for your system

## Management

Use the management script for common operations:

```bash
# Check status
./teamsphere-manager.sh status

# View logs
./teamsphere-manager.sh logs

# Restart application
./teamsphere-manager.sh restart
```

## Uninstallation

To remove TeamSphere:
```bash
./teamsphere-uninstaller.sh
```

For detailed instructions, see README.md
