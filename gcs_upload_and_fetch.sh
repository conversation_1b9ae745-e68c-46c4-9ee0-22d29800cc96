#!/bin/bash

set -e  # Exit on any error

# === CONFIGURE these variables ===
PROJECT_ID="ops-excellence"
BUCKET_NAME="teamsphere-bucket-$(date +%s)"
LOCAL_BASE_PATH="/home/<USER>/Documents/teamsphere"
VM_NAME="teamsphere-staging"
ZONE="asia-south2-c"
REMOTE_PATH="/home/<USER>/"
REMOTE_PATH_PIYUSH="/home/<USER>/"
CLEANUP=true

# Directories and files to upload
UPLOAD_ITEMS=(
    "build/teamsphere-installer.bin"
)

# === Function to check for gcloud and install if missing ===
install_gcloud() {
  if ! command -v gcloud &> /dev/null; then
    echo "gcloud not found. Installing Google Cloud SDK..."
    curl https://sdk.cloud.google.com | bash
    exec -l $SHELL
  else
    echo "✅ gcloud found."
  fi
}

# === Authenticate and configure gcloud ===
auth_gcloud() {
  echo "🔐 Authenticating and setting project..."
  gcloud auth login --quiet
  gcloud config set project "$PROJECT_ID"
}

# === Create bucket ===
create_bucket() {
  # Use a valid GCS region (not a VM zone)
  BUCKET_REGION="asia-south2"
  echo "🪣 Creating bucket: $BUCKET_NAME in region $BUCKET_REGION"
  gsutil mb -l "$BUCKET_REGION" "gs://$BUCKET_NAME/"
}

# === Upload files and directories to bucket ===
upload_files() {
  echo "⬆️ Uploading TeamSphere components to gs://$BUCKET_NAME/"

  # Change to the base directory
  cd "$LOCAL_BASE_PATH"

  # Upload each item in the list
  for item in "${UPLOAD_ITEMS[@]}"; do
    if [ -d "$item" ]; then
      echo "⬆️ Uploading directory: $item"
      gsutil -m cp -r "$item" "gs://$BUCKET_NAME/"
    elif [ -f "$item" ]; then
      echo "⬆️ Uploading file: $item"
      gsutil cp "$item" "gs://$BUCKET_NAME/"
    else
      echo "⚠️ Warning: $item not found, skipping..."
    fi
  done

  echo "✅ All components uploaded successfully"
}

# === SSH into VM and download files ===
download_on_vm() {
  echo "📥 Connecting to VM and downloading components..."
  gcloud compute ssh "$VM_NAME" --zone "$ZONE" --command "
    # Create base directories if they don't exist and set proper permissions
    echo '📁 Creating directory: $REMOTE_PATH'
    sudo mkdir -p $REMOTE_PATH
    sudo chown vbs_tms:vbs_tms $REMOTE_PATH

    echo '📁 Creating directory: $REMOTE_PATH_PIYUSH'
    sudo mkdir -p $REMOTE_PATH_PIYUSH
    sudo chown piyush_mishra:piyush_mishra $REMOTE_PATH_PIYUSH 2>/dev/null || sudo chown \$(whoami):\$(whoami) $REMOTE_PATH_PIYUSH

    # Note: No need to create subdirectories since we're only downloading the installer binary

    # Download installer binary to primary location (/home/<USER>/)
    echo '📥 Downloading to primary location: ${REMOTE_PATH}'
    echo '📥 Downloading teamsphere-installer.bin...'
    gsutil cp gs://$BUCKET_NAME/build/teamsphere-installer.bin ${REMOTE_PATH}

    # Download installer binary to secondary location (/home/<USER>/)
    echo '📥 Downloading to secondary location: ${REMOTE_PATH_PIYUSH}'
    echo '📥 Downloading teamsphere-installer.bin...'
    gsutil cp gs://$BUCKET_NAME/build/teamsphere-installer.bin ${REMOTE_PATH_PIYUSH}

    # Set proper ownership and permissions for primary location
    sudo chown vbs_tms:vbs_tms ${REMOTE_PATH}teamsphere-installer.bin
    sudo chmod +x ${REMOTE_PATH}teamsphere-installer.bin

    # Set proper ownership and permissions for secondary location
    sudo chown piyush_mishra:piyush_mishra ${REMOTE_PATH_PIYUSH}teamsphere-installer.bin 2>/dev/null || sudo chown \$(whoami):\$(whoami) ${REMOTE_PATH_PIYUSH}teamsphere-installer.bin
    sudo chmod +x ${REMOTE_PATH_PIYUSH}teamsphere-installer.bin

    # Verify downloads
    echo '✅ Download verification:'
    echo 'Contents of ${REMOTE_PATH}:'
    ls -la $REMOTE_PATH
    echo 'Installer binary in primary location:'
    ls -la ${REMOTE_PATH}teamsphere-installer.bin
    echo 'Contents of ${REMOTE_PATH_PIYUSH}:'
    ls -la $REMOTE_PATH_PIYUSH
    echo 'Installer binary in secondary location:'
    ls -la ${REMOTE_PATH_PIYUSH}teamsphere-installer.bin
  "
}

# === Clean up bucket files ===
cleanup_bucket() {
  if [ "$CLEANUP" = true ]; then
    echo "🧹 Cleaning up GCS bucket files..."

    # Remove all uploaded items
    for item in "${UPLOAD_ITEMS[@]}"; do
      echo "🗑️ Removing $item from bucket..."
      gsutil -m rm -r "gs://$BUCKET_NAME/$item" 2>/dev/null || echo "⚠️ Could not remove $item (may not exist)"
    done

    echo "✅ Bucket cleanup completed"
  fi
}

# === MAIN EXECUTION ===

install_gcloud
auth_gcloud
create_bucket
upload_files
download_on_vm
cleanup_bucket

echo "✅ DONE: TeamSphere installer uploaded to GCS and downloaded to VM $VM_NAME at:"
echo "  📁 Primary location: ${REMOTE_PATH}teamsphere-installer.bin"
echo "  📁 Secondary location: ${REMOTE_PATH_PIYUSH}teamsphere-installer.bin"

# === Execute TeamSphere installer on VM ===
execute_installer() {
  echo "🚀 Executing TeamSphere installer on VM..."
  echo "📍 Using primary location: ${REMOTE_PATH}teamsphere-installer.bin"
  gcloud compute ssh "$VM_NAME" --zone "$ZONE" --command "
    cd $REMOTE_PATH

    # Make installer executable and run it
    sudo chmod +x teamsphere-installer.bin
    echo '🔧 Running teamsphere-installer.bin from ${REMOTE_PATH}...'
    sudo ./teamsphere-installer.bin

    echo '✅ TeamSphere installer completed from primary location'
  "
}

# === Execute TeamSphere installer from secondary location ===
execute_installer_piyush() {
  echo "🚀 Executing TeamSphere installer from secondary location..."
  echo "📍 Using secondary location: ${REMOTE_PATH_PIYUSH}teamsphere-installer.bin"
  gcloud compute ssh "$VM_NAME" --zone "$ZONE" --command "
    cd $REMOTE_PATH_PIYUSH

    # Make installer executable and run it
    sudo chmod +x teamsphere-installer.bin
    echo '🔧 Running teamsphere-installer.bin from ${REMOTE_PATH_PIYUSH}...'
    sudo ./teamsphere-installer.bin

    echo '✅ TeamSphere installer completed from secondary location'
  "
}

# Execute TeamSphere installer
execute_installer

echo "🎉 TeamSphere installation process completed!"
echo "🌐 Try accessing: http://teamsphere.in"
echo "🔧 Backend API: http://api.teamsphere.in"
echo ""
echo "📝 Note: Installer binary is available in both locations:"
echo "  📁 Primary: ${REMOTE_PATH}teamsphere-installer.bin (executed by default)"
echo "  📁 Secondary: ${REMOTE_PATH_PIYUSH}teamsphere-installer.bin (available for manual use)"
echo ""
echo "💡 To execute installer from the secondary location, you can manually run:"
echo "  execute_installer_piyush"

