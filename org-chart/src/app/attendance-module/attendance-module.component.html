<div class="admin-dashboard">
    <div class="actions">
      <button mat-raised-button color="primary" class="action-button add-button" (click)="openAddTimeSheetForm()"
        [disabled]="!hasEditAccess()" matTooltip="You don't have access to perform this action"
        [matTooltipDisabled]="hasEditAccess()">
        <mat-icon>add</mat-icon>Add Record
      </button>
  
      <div class="upload-container">
        <button mat-raised-button color="primary" class="action-button upload-button" (click)="fileInput.click()"
          [disabled]="!hasEditAccess()" matTooltip="You don't have access to perform this action"
          [matTooltipDisabled]="hasEditAccess()">
          <mat-icon>upload</mat-icon> Upload CSV
        </button>
        <mat-icon class="info-icon" (click)="openCsvInfoDialog()" matTooltip="CSV Format Info">info</mat-icon>
      </div>
  
      <button mat-raised-button color="primary" class="action-button bitrix-button" (click)="generateFromBitrix()"
        [disabled]="!hasEditAccess()" matTooltip="Generate attendance data from Bitrix"
        [matTooltipDisabled]="hasEditAccess()">
        <mat-icon>sync</mat-icon> Sync Bitrix
      </button>
  
      <input
        #fileInput
        type="file"
        accept=".csv"
        (change)="onFileUpload($event)"
        style="display: none"
      />
  
      <button mat-raised-button color="primary" class="action-button download-button" (click)="downloadCSV()">
        <mat-icon>download</mat-icon> Download CSV
      </button>
      
      <button mat-raised-button color="warn" 
        (click)="deleteSelectedRecords()"  
        [disabled]="!selectedRecords.length || !hasEditAccess()"
        matTooltip="You don't have access to perform this action"
        [matTooltipDisabled]="hasEditAccess()">
        <mat-icon>delete</mat-icon> Delete Selected
      </button>
  
      <mat-checkbox [(ngModel)]="showFilters">Show Filters</mat-checkbox>
    
      <div class="dropdown">
        <button class="dropdown-button" (click)="toggleDropdown()">
          <mat-icon>arrow_drop_down_circle</mat-icon> Select Columns 
        </button>
        <div class="dropdown-menu" *ngIf="dropdownOpen">
          <input type="text" [(ngModel)]="searchText" (input)="filterColumns()" placeholder="Search columns..." class="search-box" />
          <label class="checkbox-container">
            <input type="checkbox" (change)="toggleSelectAll()" />
            Select All
          </label>
          <div class="column-list">
            <label class="checkbox-container" *ngFor="let col of filteredColumns">
              <input type="checkbox" 
                    [checked]="selectedColumns.includes(col.key)" 
                    (change)="updateDisplayedColumns($event, col.key)" />
              {{ col.displayName }}
            </label>
          </div>
        </div>
      </div>
        
      <mat-form-field appearance="outline" class="search-field">
        <mat-label>Search</mat-label>
        <mat-icon matPrefix>search</mat-icon>
        <input matInput (keyup)="applyGlobalFilter($event)" placeholder="Search records">
      </mat-form-field>
    </div>
  
    <div class="table-responsive">
      <mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8">
        <!-- Checkbox Column -->
        <ng-container matColumnDef="select">
          <mat-header-cell *matHeaderCellDef>
            <mat-checkbox 
              (change)="masterToggle()"
              [checked]="isAllSelected()"
              [indeterminate]="isIndeterminate()">
            </mat-checkbox>
          </mat-header-cell>
          <mat-cell *matCellDef="let record">
            <mat-checkbox 
              (click)="$event.stopPropagation()"
              (change)="toggleSelection(record)"
              [checked]="isSelected(record)">
            </mat-checkbox>
          </mat-cell>
        </ng-container>
  
        <!-- ID Column -->
        <ng-container matColumnDef="id">
          <mat-header-cell *matHeaderCellDef mat-sort-header>
            <div class="header-container">
              <span class="header-text">ID</span>
              <input *ngIf="showFilters" matInput class="filter-input" (keyup)="applyFilter($event, 'id')" placeholder="Filter ID">
            </div>
          </mat-header-cell>
          <mat-cell *matCellDef="let record">{{ record.id }}</mat-cell>
        </ng-container>
  
        <!-- User ID Column -->
        <ng-container matColumnDef="ldap">
          <mat-header-cell *matHeaderCellDef mat-sort-header>
            <div class="header-container">
              <span class="header-text">User ID</span>
              <input *ngIf="showFilters" matInput class="filter-input" (keyup)="applyFilter($event, 'ldap')" placeholder="Filter User ID">
            </div>
          </mat-header-cell>
          <mat-cell *matCellDef="let record">{{ record.ldap }}</mat-cell>
        </ng-container>
  
        <!-- Masked Org ID Column -->
        <ng-container matColumnDef="masked_orgid">
          <mat-header-cell *matHeaderCellDef mat-sort-header>
            <div class="header-container">
              <span class="header-text">Masked Org ID</span>
              <input *ngIf="showFilters" matInput class="filter-input" (keyup)="applyFilter($event, 'masked_orgid')" placeholder="Filter Masked Org ID">
            </div>
          </mat-header-cell>
          <mat-cell *matCellDef="let record">{{ record.masked_orgid }}</mat-cell>
        </ng-container>
  
        <!-- Subrole Column -->
        <ng-container matColumnDef="subrole">
          <mat-header-cell *matHeaderCellDef mat-sort-header>
            <div class="header-container">
              <span class="header-text">Subrole</span>
              <input *ngIf="showFilters" matInput class="filter-input" (keyup)="applyFilter($event, 'subrole')" placeholder="Filter Subrole">
            </div>
          </mat-header-cell>
          <mat-cell *matCellDef="let record">{{ record.subrole }}</mat-cell>
        </ng-container>
  
        <!-- Role Column -->
        <ng-container matColumnDef="role">
          <mat-header-cell *matHeaderCellDef mat-sort-header>
            <div class="header-container">
              <span class="header-text">Role</span>
              <input *ngIf="showFilters" matInput class="filter-input" (keyup)="applyFilter($event, 'role')" placeholder="Filter Role">
            </div>
          </mat-header-cell>
          <mat-cell *matCellDef="let record">{{ record.role }}</mat-cell>
        </ng-container>
  
        <!-- Date Column -->
        <ng-container matColumnDef="date">
          <mat-header-cell *matHeaderCellDef mat-sort-header>
            <div class="header-container">
              <span class="header-text">Date</span>
              <input *ngIf="showFilters" matInput class="filter-input" (keyup)="applyFilter($event, 'date')" placeholder="Filter Date">
            </div>
          </mat-header-cell>
          <mat-cell *matCellDef="let record">{{ record.date }}</mat-cell>
        </ng-container>
  
        <!-- Process Column -->
        <ng-container matColumnDef="process">
          <mat-header-cell *matHeaderCellDef mat-sort-header>
            <div class="header-container">
              <span class="header-text">Process</span>
              <input *ngIf="showFilters" matInput class="filter-input" (keyup)="applyFilter($event, 'process')" placeholder="Filter Process">
            </div>
          </mat-header-cell>
          <mat-cell *matCellDef="let record">{{ record.process }}</mat-cell>
        </ng-container>
  
        <!-- Billing Code Column -->
        <ng-container matColumnDef="billingCode">
          <mat-header-cell *matHeaderCellDef mat-sort-header>
            <div class="header-container">
              <span class="header-text">Billing Code</span>
              <input *ngIf="showFilters" matInput class="filter-input" (keyup)="applyFilter($event, 'billingCode')" placeholder="Filter Billing Code">
            </div>
          </mat-header-cell>
          <mat-cell *matCellDef="let record">{{ record.billingCode }}</mat-cell>
        </ng-container>
  
        <!-- Activity Column -->
        <ng-container matColumnDef="activity">
          <mat-header-cell *matHeaderCellDef mat-sort-header>
            <div class="header-container">
              <span class="header-text">Activity</span>
              <input *ngIf="showFilters" matInput class="filter-input" (keyup)="applyFilter($event, 'activity')" placeholder="Filter Activity">
            </div>
          </mat-header-cell>
          <mat-cell *matCellDef="let record">{{ record.activity }}</mat-cell>
        </ng-container>
  
        <!-- Status Column -->
        <ng-container matColumnDef="status">
          <mat-header-cell *matHeaderCellDef mat-sort-header>
            <div class="header-container">
              <span class="header-text">Status</span>
              <input *ngIf="showFilters" matInput class="filter-input" (keyup)="applyFilter($event, 'status')" placeholder="Filter Status">
            </div>
          </mat-header-cell>
          <mat-cell *matCellDef="let record">{{ record.status }}</mat-cell>
        </ng-container>
  
        <!-- Lead User ID Column -->
        <ng-container matColumnDef="lead_ldap">
          <mat-header-cell *matHeaderCellDef mat-sort-header>
            <div class="header-container">
              <span class="header-text">Lead User ID</span>
              <input *ngIf="showFilters" matInput class="filter-input" (keyup)="applyFilter($event, 'lead_ldap')" placeholder="Filter Lead User ID">
            </div>
          </mat-header-cell>
          <mat-cell *matCellDef="let record">{{ record.lead_ldap }}</mat-cell>
        </ng-container>
  
        <!-- Vendor Column -->
        <ng-container matColumnDef="vendor">
          <mat-header-cell *matHeaderCellDef mat-sort-header>
            <div class="header-container">
              <span class="header-text">Vendor</span>
              <input *ngIf="showFilters" matInput class="filter-input" (keyup)="applyFilter($event, 'vendor')" placeholder="Filter Vendor">
            </div>
          </mat-header-cell>
          <mat-cell *matCellDef="let record">{{ record.vendor }}</mat-cell>
        </ng-container>
  
        <!-- Minutes Column -->
        <ng-container matColumnDef="minutes">
          <mat-header-cell *matHeaderCellDef mat-sort-header>
            <div class="header-container">
              <span class="header-text">Minutes</span>
              <input *ngIf="showFilters" matInput class="filter-input" (keyup)="applyFilter($event, 'minutes')" placeholder="Filter Minutes">
            </div>
          </mat-header-cell>
          <mat-cell *matCellDef="let record">{{ record.minutes }}</mat-cell>
        </ng-container>
  
        <!-- Project Column -->
        <ng-container matColumnDef="project">
          <mat-header-cell *matHeaderCellDef mat-sort-header>
            <div class="header-container">
              <span class="header-text">Project</span>
              <input *ngIf="showFilters" matInput class="filter-input" (keyup)="applyFilter($event, 'project')" placeholder="Filter Project">
            </div>
          </mat-header-cell>
          <mat-cell *matCellDef="let record">{{ record.project }}</mat-cell>
        </ng-container>
  
        <!-- Team Column -->
        <ng-container matColumnDef="team">
          <mat-header-cell *matHeaderCellDef mat-sort-header>
            <div class="header-container">
              <span class="header-text">Team</span>
              <input *ngIf="showFilters" matInput class="filter-input" (keyup)="applyFilter($event, 'team')" placeholder="Filter Team">
            </div>
          </mat-header-cell>
          <mat-cell *matCellDef="let record">{{ record.team }}</mat-cell>
        </ng-container>
  
        <!-- Comment Column -->
        <ng-container matColumnDef="comment">
          <mat-header-cell *matHeaderCellDef mat-sort-header>
            <div class="header-container">
              <span class="header-text">Comment</span>
              <input *ngIf="showFilters" matInput class="filter-input" (keyup)="applyFilter($event, 'comment')" placeholder="Filter Comment">
            </div>
          </mat-header-cell>
          <mat-cell *matCellDef="let record">{{ record.comment }}</mat-cell>
        </ng-container>
  
        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <mat-header-cell *matHeaderCellDef>Actions</mat-header-cell>
          <mat-cell *matCellDef="let record">
            <!-- <button mat-raised-button color="accent" class="action-button view-button" (click)="viewRecord(record)">View</button> -->
            <button mat-raised-button color="primary" class="action-button edit-button" 
              (click)="editRecord(record)"
              [disabled]="!hasEditAccess()" 
              matTooltip="You don't have access to perform this action"
              [matTooltipDisabled]="hasEditAccess()">            
              Edit
            </button>
            <button mat-raised-button color="warn" class="action-button delete-button" 
              (click)="deleteRecord(record)"
              [disabled]="!hasEditAccess()" 
              matTooltip="You don't have access to perform this action"
              [matTooltipDisabled]="hasEditAccess()">
              Delete
            </button>
          </mat-cell>
        </ng-container>
  
        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
      </mat-table>
    </div>
  
    <mat-paginator
      [length]="dataSource.data.length"
      [pageSize]="5"
      [pageSizeOptions]="[5, 10, 20]">
    </mat-paginator>
  
    <ng-template #csvInfoDialog>
      <div class="header-csv">
        <h2 mat-dialog-title>CSV Format Information</h2>
        <mat-dialog-actions>
          <button mat-icon-button mat-dialog-close class="action-button delete-button">
            <mat-icon>close</mat-icon>
          </button>
        </mat-dialog-actions>
      </div>
      <mat-dialog-content>
        <p>Note:</p>
        <ul>
          <li>Make sure all required fields are filled in correctly.</li>
          <li>Dates should be in YYYY-MM-DD format.</li>
          <li>Minutes should be numeric values.</li>
        </ul>
        <p>Your CSV file should have the following format:</p>
        <table mat-table [dataSource]="csvFormatExamples" class="csv-format-table">
          <ng-container matColumnDef="columnName">
            <mat-header-cell *matHeaderCellDef>Column Name</mat-header-cell>
            <mat-cell *matCellDef="let element">{{ element.columnName }}</mat-cell>
          </ng-container>
    
          <ng-container matColumnDef="description">
            <mat-header-cell *matHeaderCellDef>Description</mat-header-cell>
            <mat-cell *matCellDef="let element">{{ element.description }}</mat-cell>
          </ng-container>
    
          <mat-header-row *matHeaderRowDef="['columnName', 'description']"></mat-header-row>
          <mat-row *matRowDef="let row; columns: ['columnName', 'description'];"></mat-row>
        </table>
        <p>
          Example:
          <code>
            1,user1,org123,Developer,Engineer,2023-05-15,Development,DEV001,Coding,Completed,lead1,Vendor A,480,Project X,Team Alpha,Completed sprint tasks
          </code>
        </p>
      </mat-dialog-content>
    </ng-template>  
</div>