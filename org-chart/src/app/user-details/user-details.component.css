/* Modern User Details Styles */
.user-details {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  font-family: '<PERSON>', '<PERSON>o', sans-serif;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

/* Page Header */
.page-header {
  margin-bottom: 32px;
}

.header-content {
  text-align: center;
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.title-icon {
  font-size: 2.5rem !important;
  width: 2.5rem !important;
  height: 2.5rem !important;
  color: #667eea !important;
}

.page-subtitle {
  font-size: 1.1rem;
  color: #718096;
  margin: 0;
  font-weight: 400;
}

/* Profile Hero Section */
.profile-hero {
  margin-bottom: 32px;
  border-radius: 20px !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.hero-content {
  display: flex;
  align-items: center;
  gap: 32px;
  padding: 40px;
}

.profile-avatar {
  flex-shrink: 0;
}

.avatar-container {
  position: relative;
  width: 120px;
  height: 120px;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid #ffffff;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
}

.avatar-image:hover {
  transform: scale(1.05);
}

.status-indicator {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 3px solid #ffffff;
  background-color: #e53e3e;
}

.status-indicator.active {
  background-color: #38a169;
}

.profile-info {
  flex: 1;
}

.user-name {
  font-size: 2.25rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 16px 0;
  line-height: 1.2;
}

.user-role,
.user-team {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 1.1rem;
  color: #4a5568;
}

.role-icon,
.team-icon {
  color: #667eea !important;
  font-size: 1.25rem !important;
}

.contact-info {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  margin-top: 20px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  color: #4a5568;
}

.contact-icon {
  color: #667eea !important;
  font-size: 1.1rem !important;
}

.email-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.email-link:hover {
  color: #5a67d8;
  text-decoration: underline;
}

/* Information Cards Grid */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.info-card {
  border-radius: 16px !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  background: #ffffff !important;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  overflow: hidden;
}

.info-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
}

.card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  padding: 20px 24px !important;
  margin: 0 !important;
}

.section-icon {
  color: white !important;
  font-size: 1.5rem !important;
  margin-right: 12px !important;
}

.section-title {
  color: white !important;
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  margin: 0 !important;
}

.card-content {
  padding: 24px !important;
}

.info-grid-items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.info-item {
  padding: 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.info-item:hover {
  background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.info-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.field-icon {
  color: #667eea !important;
  font-size: 1rem !important;
}

.info-value {
  font-size: 1rem;
  font-weight: 500;
  color: #1a202c;
  line-height: 1.4;
  word-break: break-word;
}

/* Status Badge */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.active {
  background-color: #c6f6d5;
  color: #22543d;
  border: 1px solid #9ae6b4;
}

.status-badge.inactive {
  background-color: #fed7d7;
  color: #742a2a;
  border: 1px solid #fc8181;
}

/* Responsive Design */
@media (max-width: 768px) {
  .user-details {
    padding: 16px;
  }

  .hero-content {
    flex-direction: column;
    text-align: center;
    padding: 24px;
    gap: 24px;
  }

  .user-name {
    font-size: 1.875rem;
  }

  .page-title {
    font-size: 2rem;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .info-grid-items {
    grid-template-columns: 1fr;
  }

  .contact-info {
    flex-direction: column;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .avatar-container {
    width: 100px;
    height: 100px;
  }

  .user-name {
    font-size: 1.5rem;
  }

  .page-title {
    font-size: 1.75rem;
  }

  .card-header {
    padding: 16px 20px !important;
  }

  .card-content {
    padding: 20px !important;
  }
}

/* Animation for smooth loading */
.info-card {
  animation: fadeInUp 0.6s ease-out;
}

.info-card:nth-child(1) { animation-delay: 0.1s; }
.info-card:nth-child(2) { animation-delay: 0.2s; }
.info-card:nth-child(3) { animation-delay: 0.3s; }
.info-card:nth-child(4) { animation-delay: 0.4s; }
.info-card:nth-child(5) { animation-delay: 0.5s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
