.forgot-password-container {
  max-width: 400px;
  padding: 30px;
  background: linear-gradient(to bottom, #ffffff, #f7f7f7);
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  text-align: center;

  /* Centering both horizontally and vertically */
  position: absolute;
  top: 45%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.logo {
  width: 250px;
  height: 100px;
  margin-bottom: 20px;
}

h2 {
  font-size: 26px;
  color: #333;
  margin-bottom: 8px;
}

.subtitle {
  font-size: 16px;
  color: #666;
  margin-bottom: 24px;
}

/* Form Fields */
.custom-field {
  width: 100%;
  margin-bottom: 20px;
}

.custom-field.mat-form-field {
  border-radius: 8px;
  overflow: hidden;
}

/* Buttons */
.action-button {
  width: 100%;
  padding: 14px;
  font-size: 16px;
  font-weight: bold;
  background-color: #111411;
  color: #fff;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.action-button:hover {
  background-color: #3f29e6;
  transform: scale(1.05);
}

.back-to-login {
  text-align: center;
  margin-top: 15px;
  font-size: 14px;
}

.link {
  color: #134472;
  text-decoration: none;
  font-weight: 500;
}

.link:hover {
  text-decoration: underline;
}

/* Responsive Design */
@media screen and (max-width: 480px) {
  .forgot-password-container {
    padding: 20px;
    margin: 20px;
  }

  h2 {
    font-size: 22px;
  }

  .subtitle {
    font-size: 14px;
  }

  .action-button {
    padding: 12px;
    font-size: 14px;
  }
}