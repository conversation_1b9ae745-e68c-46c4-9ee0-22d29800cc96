import { Component, OnInit, OnDestroy } from '@angular/core';
import { UserService } from '../user.service';
import { AuthService } from '../auth.service';
import { Router } from '@angular/router';
import { PermissionService, UserRole } from '../shared/permission.service';
import { jwtDecode } from 'jwt-decode';
import { NotificationCountService, RequestCounts } from '../shared/notification-count.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.css']
})
export class SidebarComponent implements OnInit, OnDestroy {
  showForm = false;
  collapsed = false;
  userName: string = '';
  userRole: string = '';
  isAdminUser = false;
  requestCounts: RequestCounts = { pending: 0, approved: 0, rejected: 0, total: 0 };
  private countSubscription: Subscription | undefined;

  constructor(
    private userService: UserService,
    private authService: AuthService,
    private router: Router,
    private permissionService: PermissionService,
    private notificationCountService: NotificationCountService
  ) {
    this.getUserInfo();
  }

  ngOnInit(): void {
    // Subscribe to role changes
    this.authService.role$.subscribe(role => {
      if (role) {
        this.verifyAndUpdateRole();
      }
    });

    // Initial verification
    this.verifyAndUpdateRole();

    // Subscribe to request counts for admin users
    this.countSubscription = this.notificationCountService.requestCounts$.subscribe(
      counts => {
        this.requestCounts = counts;
      }
    );
  }

  ngOnDestroy(): void {
    if (this.countSubscription) {
      this.countSubscription.unsubscribe();
    }
  }

  toggleSidebar() {
    this.collapsed = !this.collapsed;
  }

  toggleForm() {
    this.showForm = !this.showForm;
  }

  /**
   * Verifies the user's role from the JWT token and updates local state
   * This prevents users from manipulating localStorage to gain unauthorized access
   */
  verifyAndUpdateRole(): void {
    const token = this.authService.getToken();
    const storedRole = localStorage.getItem('role');

    if (!token) {
      // No token, user should be logged out
      this.isAdminUser = false;
      this.router.navigate(['/login']);
      return;
    }

    try {
      // Decode the JWT token to get the role claim
      const decodedToken: any = jwtDecode(token);
      const tokenRole = decodedToken.role;

      // Check if the role in localStorage matches the role in the token
      if (tokenRole !== storedRole) {
        console.error('Role mismatch detected! Token role:', tokenRole, 'Stored role:', storedRole);
        // Role mismatch detected, force logout
        this.authService.logout();
        this.isAdminUser = false;
        return;
      }

      // Use the permission service to check if the user has admin privileges
      const adminRoles: UserRole[] = ['LEAD', 'MANAGER', 'ACCOUNT_MANAGER', 'ADMIN_OPS_MANAGER'] as UserRole[];
      this.permissionService.hasAnyRole(adminRoles).subscribe(hasAdminRole => {
        this.isAdminUser = hasAdminRole;
      });

    } catch (error) {
      console.error('Error verifying user role:', error);
      // Token is invalid or tampered with, force logout
      this.authService.logout();
      this.isAdminUser = false;
    }
  }

  /**
   * Secure method to check if the user has admin privileges
   * Uses the verified role status instead of directly checking localStorage
   */
  isAdmin(): boolean {
    return this.isAdminUser;
  }

  logout() {
    this.userService.isAdmin = false;
    this.authService.logout();
    this.router.navigate(['/login']);
  }

  /**
   * Gets user information and triggers role verification
   * This ensures that the displayed user info is consistent with the verified role
   */
  getUserInfo() {
    this.userName = localStorage.getItem('username') || 'User';
    this.userRole = localStorage.getItem('role') || 'USER';

    // Verify role whenever user info is accessed
    this.verifyAndUpdateRole();
  }

  /**
   * Get tooltip text for the request notification badge
   */
  getRequestTooltip(): string {
    const pendingCount = this.requestCounts.pending;
    const totalCount = this.requestCounts.total;

    if (pendingCount === 0) {
      return 'No pending requests';
    } else if (pendingCount === 1) {
      return `1 pending request out of ${totalCount} total`;
    } else {
      return `${pendingCount} pending requests out of ${totalCount} total`;
    }
  }
}
