html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden; /* Prevents any overflow on the page */
}

/* Login Container */
/* .login-container {
  max-width: 400px;
  margin: 80px auto;
  padding: 30px;
  background: linear-gradient(to bottom, #ffffff, #f7f7f7);
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  text-align: center;
  margin: 0 auto;

} */
.login-container {
  max-width: 400px;
  padding: 30px;
  background: linear-gradient(to bottom, #ffffff, #f7f7f7);
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  text-align: center;

  /* Centering both horizontally and vertically */
  position: absolute;
  top: 45%;
  left: 50%;
  transform: translate(-50%, -50%);
}


.logo {
  width: 100px;
  margin-bottom: 20px;
}

h2 {
  font-size: 26px;
  color: #333;
  margin-bottom: 8px;
}

.login-subtitle {
  font-size: 16px;
  color: #666;
  margin-bottom: 24px;
}

/* Form Fields */
.custom-field {
  width: 100%;
  margin-bottom: 20px;
}

.custom-field.mat-form-field {
  border-radius: 8px;
  overflow: hidden;
}

mat-label {
  font-size: 14px;
  color: #333;
}

input::placeholder {
  color: #aaa;
  font-size: 14px;
}

/* Buttons */
.action-button {
  width: 100%;
  padding: 14px;
  font-size: 16px;
  font-weight: bold;
  background-color: #111411;
  color: #fff;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.action-button:hover {
  background-color: #3f29e6;
  transform: scale(1.05);
}

/* Footer Text */
.footer-text {
  font-size: 14px;
  color: #999;
  margin-top: 20px;
}

.link {
  color: #117cbe;
  text-decoration: none;
}

.link:hover {
  text-decoration: underline;
}

.forgot-password {
  text-align: center;
  margin-top: 15px;
  font-size: 14px;
}

.forgot-password .link {
  color: #134472;
  font-weight: 500;
}

/* Responsive Design */
@media screen and (max-width: 480px) {
  .login-container {
    padding: 20px;
    margin: 20px;
  }

  h2 {
    font-size: 22px;
  }

  .login-subtitle {
    font-size: 14px;
  }

  .action-button {
    padding: 12px;
    font-size: 14px;
  }
}

.logo {
  width: 250px;
  height: 100px;
}

/* Maintenance Overlay */
.maintenance-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.close-maintenance {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.9);
  color: #134472;
  z-index: 1001;
}

.close-maintenance:hover {
  background: rgba(255, 255, 255, 1);
}

.hidden {
  display: none;
}
