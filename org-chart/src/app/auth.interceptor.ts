import { Injectable } from '@angular/core';
import { <PERSON>ttp<PERSON><PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { finalize, catchError } from 'rxjs/operators';
import { AuthService } from './auth.service';
import { LoaderService } from './shared/loader.service';
import { Router } from '@angular/router';
import {jwtDecode} from 'jwt-decode'
import { NotificationService } from './shared/notification.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  private activeRequests = 0;

  constructor(
    private authService: AuthService,
    private loaderService: LoaderService,
    private router: Router,
    private notificationService: NotificationService
  ) {}

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    const token = this.authService.getToken();

    // Increment the active requests counter
    this.activeRequests++;

    // Only show the loader if this is the first active request
    if (this.activeRequests === 1) {
      setTimeout(() => {
        this.loaderService.showLoader();
      }, 0);
    }

    // Skip token expiration check for logout requests to prevent recursion
    const isLogoutRequest = req.url.includes('/auth/logout');

    if (token && !isLogoutRequest && this.isTokenExpired(token)) {
      this.authService.logout();

      this.notificationService.showNotification({
        type: 'error',
        message: 'Session expired. Please login again.'
      });

      // Decrement the counter and hide loader if no more requests
      this.activeRequests--;
      if (this.activeRequests === 0) {
        setTimeout(() => {
          this.loaderService.hideLoader();
        }, 0);
      }

      this.router.navigate(['/login']);
      return of();
    }

    let modifiedRequest = req;

    // Get CSRF token from cookies if available
    const csrfToken = this.getCsrfToken();

    if (token) {
      const headers: { [key: string]: string } = {
        Authorization: `Bearer ${token}`
      };

      // Add CSRF token for non-GET requests if available
      if (csrfToken && req.method !== 'GET') {
        headers['X-XSRF-TOKEN'] = csrfToken;
      }

      modifiedRequest = req.clone({
        setHeaders: headers
      });
    }

    return next.handle(modifiedRequest).pipe(
      finalize(() => {
        // Decrement the counter
        this.activeRequests--;

        // Only hide the loader if there are no more active requests
        if (this.activeRequests === 0) {
          setTimeout(() => {
            this.loaderService.hideLoader();
          }, 0);
        }
      }),
      catchError((error) => {
        // Skip logout for logout requests to prevent recursion
        const isLogoutRequest = req.url.includes('/auth/logout');

        if (error.status === 401 && !isLogoutRequest) {
          this.authService.logout();

          this.notificationService.showNotification({
            type: 'error',
            message: 'Session expired. Please login again.'
          });
          this.router.navigate(['/login']);
        }
        throw error;
      })
    );
  }

  private isTokenExpired(token: string): boolean {
    try {
      const decodedToken: any = jwtDecode(token);
      const expirationDate = new Date(0);
      expirationDate.setUTCSeconds(decodedToken.exp);
      return expirationDate < new Date();
    } catch (error) {
      console.error('Error decoding token:', error);
      return true;
    }
  }

  /**
   * Get CSRF token from cookies
   * @returns CSRF token or null if not found
   */
  private getCsrfToken(): string | null {
    const cookies = document.cookie.split(';');
    for (const cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === 'XSRF-TOKEN') {
        return decodeURIComponent(value);
      }
    }
    return null;
  }
}
