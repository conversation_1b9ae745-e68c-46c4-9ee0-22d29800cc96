import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { BehaviorSubject, catchError, map, Observable, tap, throwError } from 'rxjs';
import { VunnoMgmtDto } from '../leave-application/leave-application.component';
import { environment } from 'src/environments/environment';
import { User } from '../model/user';
import { BaseResponse } from '../model/base-response.model';

@Injectable({
  providedIn: 'root'
})

export class LeaveService {
  private apiUrl = `${environment.apiUrl}/api/vunno`; // Base API URL

  private managerDetailsSubject = new BehaviorSubject<any>(null);
  managerDetails$ = this.managerDetailsSubject.asObservable();

  constructor(private http: HttpClient) { }

  // Existing method to get leave details
  getLeaveDetails(ldap: string): Observable<number[]> {
    const params = new HttpParams().set('ldap', ldap);
    return this.http.get<number[]>(`${this.apiUrl}/getLeaveDetails`, { params });
  }

  // New method to fetch leave history for a specific requestor
  getUserLeaveHistory(requestorLdap: string): Observable<any[]> {
    const params = new HttpParams().set('ldap', requestorLdap);
    return this.http.get<any[]>(`${this.apiUrl}/getHistoryForUser`, { params });
  }

  // Method to fetch the approving manager automatically for a specific ldap.
  // leave.service.ts
  getManagerByLdap(ldap: string): Observable<VunnoMgmtDto[]> {
    return this.http.get<VunnoMgmtDto[]>(
      `${this.apiUrl}/lead-manager/${ldap}`
    ).pipe(
      tap(data => this.setManagerDetails(data)), // Auto-update the BehaviorSubject
      catchError(error => {
        console.error('Error fetching manager:', error);
        return throwError(() => new Error('Failed to fetch manager details'));
      })
    );
  }

  // Call this after API fetch
  setManagerDetails(data: any): void {
    this.managerDetailsSubject.next(data);
  }

  // Optional getter (if not using .subscribe in the component)
  getManagerDetailsSnapshot(): any {
    return this.managerDetailsSubject.value;
  }

  // leave.service.ts
  submitLeaveRequest(data: FormData): Observable<any> {
    return this.http.post<any>(
      `${this.apiUrl}/requestedVunno`,
      data,
      {
        headers: {
          // DO NOT set Content-Type manually; let the browser set it for FormData
        }
      }
    ).pipe(
      catchError(error => {
        console.error('API Error:', error);
        return throwError(() => new Error('Request failed. Please try again.'));
      })
    );
  }
  

  // leave.service.ts
  getCurrentUserLdap(): Observable<BaseResponse<User>> {
    return this.http.get<BaseResponse<User>>(`${environment.apiUrl}/api/projects/current-user`);
  }

  uploadLeaveBalance(formData: FormData, force: boolean = false): Observable<any> {
    return this.http.post(`${this.apiUrl}/upload-leave-balance?force=${force}`, formData);
  }
  
  approveRequest(id: string, status: 'APPROVED' | 'DECLINED') {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${localStorage.getItem('token')}` // Secure token handling
    });

    return this.http.post(
      `${this.apiUrl}approve`,
      { id, status },
      { headers }
    );
  }

  deleteLeaveRequestById(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/deleteLeaveRequest/${id}`);
  }

  updateLeaveRequest(request: any): Observable<any> {
    const id = request.id;
    return this.http.put<any>(`${this.apiUrl}/updateLeaveRequest/${id}`, request);
  }

  getPendingRequestsForLead(): Observable<any[]> {
    return this.http.get<any>(`${this.apiUrl}/requests-for-approval`).pipe(
      map(res => res.data)
    );
  }

  approveRequestVunno(request: any): Observable<any> {
    return this.http.post(`${this.apiUrl}/approve`, request);
  }

  getApprovedRequestsForLead(): Observable<any[]> {
    return this.http.get<any>(`${this.apiUrl}/approved-requests`).pipe(
      map(res => res.data));
  }

  rejectRequestVunno(request: any): Observable<any> {
    return this.http.post(`${this.apiUrl}/reject`, request);
  }
  
  revokeRequestVunno(request:any): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/revoke`,request)
  }

  getProcessedRequestsForLead(): Observable<any[]> {
    return this.http.get<any>(`${this.apiUrl}/processed-requests-for-approval`).pipe(
      map(res => res.data));;
  }
  

}
