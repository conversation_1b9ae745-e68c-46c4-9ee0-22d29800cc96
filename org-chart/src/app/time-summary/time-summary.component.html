<div class="time-summary-container">
  <app-page-header
  title="Time Summary"
  subtitle="View and analyze time entries across projects and users">
</app-page-header>


  <div class="actions">
    <button mat-raised-button (click)="toggleColumnFilters()" class="action-button toggle-filter-button">
      <mat-icon>{{ showColumnFilters ? 'visibility_off' : 'filter_list' }}</mat-icon>
      {{ showColumnFilters ? 'Hide' : 'Show' }} Column Filters
    </button>

    <button mat-flat-button color="primary" (click)="openFilterDialog()">
      <mat-icon>filter_list</mat-icon>
      Filter Data
    </button>

    <button mat-stroked-button color="warn" (click)="resetFilters()">
      <mat-icon>clear</mat-icon>
      Clear Filters
    </button>

    <button mat-raised-button
      color="primary"
      class="action-button download-button"
      (click)="downloadCSV()">
      <mat-icon>download</mat-icon> Download CSV
    </button>

    <mat-form-field appearance="outline" class="search-field">
      <mat-label>Search</mat-label>
      <mat-icon matPrefix>search</mat-icon>
      <input matInput #globalSearchInput (keyup)="applyFilter($event)" placeholder="Search in table...">
    </mat-form-field>
  </div>

  <div class="table-responsive">
    <mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8">
        <!-- Project Name Column -->
        <ng-container matColumnDef="projectName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <div class="header-container">
              <span class="header-text">Project Name</span>
              <button mat-icon-button #projectNameTrigger="matMenuTrigger"
                      *ngIf="showColumnFilters"
                      [matMenuTriggerFor]="filterMenu"
                      (menuOpened)="openFilterMenu('projectName', projectNameTrigger)"
                      [color]="isFilterActive('projectName') ? 'accent' : ''"
                      (click)="$event.stopPropagation()">
                <mat-icon>filter_list</mat-icon>
              </button>
            </div>
          </th>
          <td mat-cell *matCellDef="let row">{{row.projectName}}</td>
        </ng-container>

        <!-- Project Code Column -->
        <ng-container matColumnDef="projectCode">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <div class="header-container">
              <span class="header-text">Project Code</span>
              <button mat-icon-button #projectCodeTrigger="matMenuTrigger"
                      *ngIf="showColumnFilters"
                      [matMenuTriggerFor]="filterMenu"
                      (menuOpened)="openFilterMenu('projectCode', projectCodeTrigger)"
                      [color]="isFilterActive('projectCode') ? 'accent' : ''"
                      (click)="$event.stopPropagation()">
                <mat-icon>filter_list</mat-icon>
              </button>
            </div>
          </th>
          <td mat-cell *matCellDef="let row">{{row.projectCode}}</td>
        </ng-container>

        <!-- Username Column -->
        <ng-container matColumnDef="username">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <div class="header-container">
              <span class="header-text">Username</span>
              <button mat-icon-button #usernameTrigger="matMenuTrigger"
                      *ngIf="showColumnFilters"
                      [matMenuTriggerFor]="filterMenu"
                      (menuOpened)="openFilterMenu('username', usernameTrigger)"
                      [color]="isFilterActive('username') ? 'accent' : ''"
                      (click)="$event.stopPropagation()">
                <mat-icon>filter_list</mat-icon>
              </button>
            </div>
          </th>
          <td mat-cell *matCellDef="let row">{{row.username}}</td>
        </ng-container>

        <!-- Total Time Column -->
        <ng-container matColumnDef="totalTimeInMins">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <div class="header-container">
              <span class="header-text">Total Time</span>
              <button mat-icon-button #totalTimeTrigger="matMenuTrigger"
                      *ngIf="showColumnFilters"
                      [matMenuTriggerFor]="filterMenu"
                      (menuOpened)="openFilterMenu('totalTimeInMins', totalTimeTrigger)"
                      [color]="isFilterActive('totalTimeInMins') ? 'accent' : ''"
                      (click)="$event.stopPropagation()">
                <mat-icon>filter_list</mat-icon>
              </button>
            </div>
          </th>
          <td mat-cell *matCellDef="let row">{{formatTimeInMins(row.totalTimeInMins)}}</td>
        </ng-container>

        <!-- Total Entries Column -->
        <ng-container matColumnDef="totalEntries">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <div class="header-container">
              <span class="header-text">Total Entries</span>
              <button mat-icon-button #totalEntriesTrigger="matMenuTrigger"
                      *ngIf="showColumnFilters"
                      [matMenuTriggerFor]="filterMenu"
                      (menuOpened)="openFilterMenu('totalEntries', totalEntriesTrigger)"
                      [color]="isFilterActive('totalEntries') ? 'accent' : ''"
                      (click)="$event.stopPropagation()">
                <mat-icon>filter_list</mat-icon>
              </button>
            </div>
          </th>
          <td mat-cell *matCellDef="let row">{{row.totalEntries}}</td>
        </ng-container>

        <!-- Start Date Column -->
        <ng-container matColumnDef="startDate">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <div class="header-container">
              <span class="header-text">Start Date</span>
              <button mat-icon-button #startDateTrigger="matMenuTrigger"
                      *ngIf="showColumnFilters"
                      [matMenuTriggerFor]="filterMenu"
                      (menuOpened)="openFilterMenu('startDate', startDateTrigger)"
                      [color]="isFilterActive('startDate') ? 'accent' : ''"
                      (click)="$event.stopPropagation()">
                <mat-icon>filter_list</mat-icon>
              </button>
            </div>
          </th>
          <td mat-cell *matCellDef="let row">{{formatDate(row.startDate)}}</td>
        </ng-container>

        <!-- End Date Column -->
        <ng-container matColumnDef="endDate">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <div class="header-container">
              <span class="header-text">End Date</span>
              <button mat-icon-button #endDateTrigger="matMenuTrigger"
                      *ngIf="showColumnFilters"
                      [matMenuTriggerFor]="filterMenu"
                      (menuOpened)="openFilterMenu('endDate', endDateTrigger)"
                      [color]="isFilterActive('endDate') ? 'accent' : ''"
                      (click)="$event.stopPropagation()">
                <mat-icon>filter_list</mat-icon>
              </button>
            </div>
          </th>
          <td mat-cell *matCellDef="let row">{{formatDate(row.endDate)}}</td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

        <!-- No Data Row -->
        <tr class="mat-row" *matNoDataRow>
          <td class="mat-cell" colspan="7">No data available</td>
        </tr>
      </mat-table>

      <mat-paginator [pageSizeOptions]="[10, 20, 30, 50, 100, 200]"
                     aria-label="Select page of time entries"
                     showFirstLastButtons>
      </mat-paginator>
  </div>

  <!-- Filter Menu Template -->
  <mat-menu #filterMenu="matMenu" class="custom-filter-menu" [overlapTrigger]="false">
    <ng-template matMenuContent>
      <div (click)="$event.stopPropagation()" style="padding: 10px;">
        <ng-container *ngIf="currentFilterMenuState.columnKey as columnKey">
          <!-- Search Input -->
          <mat-form-field appearance="outline" class="filter-search-field">
            <mat-label>Search Options</mat-label>
            <input matInput [(ngModel)]="currentFilterMenuState.searchText" placeholder="Type to search...">
          </mat-form-field>

          <!-- Select All Checkbox -->
          <mat-checkbox
            [checked]="isAllTempSelected()"
            [indeterminate]="isSomeTempSelected()"
            (change)="toggleSelectAllTemp($event.checked)">
            Select All ({{ getUniqueColumnValues(columnKey).length }} items)
          </mat-checkbox>
          <hr>
          <!-- Filter Options -->
          <div style="max-height: 200px; overflow-y: auto;">
            <mat-checkbox *ngFor="let value of filteredMenuOptions"
              [checked]="isTempSelected(value)"
              (change)="toggleTempSelection(value, $event.checked)">
              {{ value }}
            </mat-checkbox>
          </div>
          <hr>
          <!-- Action Buttons -->
          <div style="display: flex; justify-content: space-between; margin-top: 10px;">
            <button mat-button (click)="onFilterApplied()">Apply</button>
            <button mat-button (click)="clearColumnFilter()">Clear</button>
          </div>
        </ng-container>
      </div>
    </ng-template>
  </mat-menu>

  <!-- Loading Spinner -->
  <div class="loading-spinner" *ngIf="isLoading">
    <mat-spinner diameter="48"></mat-spinner>
  </div>
</div>
