<h2 mat-dialog-title>Edit Leave Request</h2>
<mat-dialog-content [formGroup]="editForm">
  <mat-form-field appearance="fill">
    <mat-label>Start Date</mat-label>
    <input matInput [matDatepicker]="startPicker" formControlName="startDate">
    <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
    <mat-datepicker #startPicker></mat-datepicker>
  </mat-form-field>

  <mat-form-field appearance="fill">
    <mat-label>End Date</mat-label>
    <input matInput [matDatepicker]="endPicker" formControlName="endDate">
    <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
    <mat-datepicker #endPicker></mat-datepicker>
  </mat-form-field>

  <mat-form-field appearance="fill">
    <mat-label>Request Type</mat-label>
    <mat-select formControlName="requestType">
      <mat-option value="Leave">Leave</mat-option>
      <mat-option value="Work From Home">Work From Home</mat-option>
    </mat-select>
  </mat-form-field>

  <mat-form-field appearance="fill">
    <mat-label>Duration</mat-label>
    <mat-select formControlName="duration">
      <mat-option value="Full Day">Full Day</mat-option>
      <mat-option value="Half Day AM">Half Day AM</mat-option>
      <mat-option value="Half Day PM">Half Day PM</mat-option>
      <mat-option value="Multiple Days">Multiple Days</mat-option>
    </mat-select>
  </mat-form-field>

  <mat-form-field appearance="fill" *ngIf="editForm.value.requestType === 'Leave'">
    <mat-label>Leave Type</mat-label>
    <mat-select formControlName="leaveType">
      <mat-option value="Casual Leave">Casual Leave</mat-option>
      <mat-option value="Sick Leave">Sick Leave</mat-option>
      <mat-option value="Earned Leave">Earned Leave</mat-option>
    </mat-select>
  </mat-form-field>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <button mat-button (click)="onCancel()">Cancel</button>
  <button mat-raised-button color="primary" (click)="onSave()">Save</button>
</mat-dialog-actions>
