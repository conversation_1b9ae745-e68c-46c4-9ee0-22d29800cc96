import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { FormGroup, FormBuilder } from '@angular/forms';

@Component({
  selector: 'app-edit-leave-request',
  templateUrl: './edit-leave-request.component.html'
})
export class EditLeaveRequestComponent {
  editForm: FormGroup;

  constructor(
    private dialogRef: MatDialogRef<EditLeaveRequestComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private fb: FormBuilder
  ) {
    this.editForm = this.fb.group({
      startDate: [data.startDate],
      endDate: [data.endDate],
      requestType: [data.requestType],
      duration: [data.duration],
      leaveType: [data.leaveType]
    });
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onSave(): void {
    if (this.editForm.valid) {
      this.dialogRef.close({
        id: this.data.id, 
        ldap: this.data.ldap, 
        approvingLead: this.data.approver, 
        applicationType: this.editForm.value.requestType, 
        lvWfhDuration: this.editForm.value.duration, 
        leaveType: this.editForm.value.leaveType, 
        startDate: this.editForm.value.startDate, 
        endDate: this.editForm.value.endDate, 
        status: this.data.status 
      });
    }
  }
}
