import { Component, Input, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { FormControl, FormGroup } from '@angular/forms';
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
import { LeaveService } from 'src/app/services/leave.service';
import { NotificationService } from 'src/app/shared/notification.service';
import { ConfirmationDialogComponent } from 'src/app/confirm-dialog/confirmation-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from "@angular/material/icon";
import { EditLeaveRequestComponent } from './edit-leave-request/edit-leave-request.component';

@Component({
  selector: 'app-history',
  templateUrl: './history.component.html',
  styleUrls: ['./history.component.css'],
})
export class HistoryComponent implements OnInit {
  @Input() ldap!: string;
  @Input() leaveHistory: any[] = [];
  @Input() userRole?: string;
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(private leaveService: LeaveService, private notificationService: NotificationService,
    private dialog:MatDialog
  ) { }

  dataSource = new MatTableDataSource<any>();
  displayedColumns: string[] = ['ldap', 'startDate', 'endDate', 'requestType','duration', 'status', 'leaveType', 'actions'];
  allColumns: string[] = [...this.displayedColumns];

  columnDisplayNames: { [key: string]: string } = {
    ldap: 'LDAP',
    leaveType: 'Leave Type',
    startDate: 'Start Date',
    endDate: 'End Date',
    requestType: 'Request Type',
    duration: 'Duration',
    status: 'Status',
    actions: 'Actions'
  };


  columnSearchText = '';
  showColumnFilters = false;

  filterValues: any = {};
  dateRange = new FormGroup({
    start: new FormControl<Date | null>(null),
    end: new FormControl<Date | null>(null)
  });

  ngOnInit(): void {
    this.fetchLeaveHistory(this.ldap);
  }

  ngAfterViewInit() {
    if(this.paginator){
      this.dataSource.paginator=this.paginator;
    }
    if(this.sort){
      this.dataSource.sort=this.sort;
    }
  }


  ngOnChanges(): void {
    if (this.ldap) {
      this.fetchLeaveHistory(this.ldap);
    }
  }

  fetchLeaveHistory(ldap: string): void {
    console.log("Inside the history component");
    this.leaveService.getUserLeaveHistory(ldap).subscribe({
      next: (data: any[]) => {
        this.leaveHistory = data.map(item => ({
          id:item["ID"],
          ldap: ldap,
          status: item["Status"],
          leaveType: item["Leave Type"],
          startDate: new Date(item["Start Date"]),
          endDate: new Date(item["End Date"]),
          requestType: item["Request Type"],
          duration: item["Duration"], // Convert to number
          approver: item["Approver"]
        }));
        this.dataSource.data = this.leaveHistory;
        // Reapply sorting after data update
        if (this.sort) {
          this.dataSource.sort = this.sort;
        }
        if (this.paginator) {
          this.paginator.firstPage();
        }
      },
      error: (error) => {
        console.error('Error fetching leave history:', error);
      }
    });
  }


  private normalizeStatus(status: string): 'Approved' | 'Pending' | 'Rejected' {
    const normalized = status.toLowerCase();
    if (normalized === 'approved') return 'Approved';
    if (normalized === 'pending') return 'Pending';
    if (normalized === 'rejected') return 'Rejected';

    // Default to Pending if status doesn't match expected values
    return 'Pending';
  }

  applyGlobalFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value.trim().toLowerCase();
    this.dataSource.filter = filterValue;
  }

  applyDateFilter(): void {
    const start = this.dateRange.get('start')?.value;
    const end = this.dateRange.get('end')?.value;
    this.dataSource.data = this.leaveHistory.filter(entry => {
      const entryDate = new Date(entry.startDate);
      return (!start || entryDate >= start) && (!end || entryDate <= end);
    });
  }

  toggleColumnFilters(): void {
    this.showColumnFilters = !this.showColumnFilters;
  }

  toggleAllColumns(checked: boolean): void {
    this.displayedColumns = checked ? [...this.allColumns] : [];
  }

  isColumnDisplayed(column: string): boolean {
    return this.displayedColumns.includes(column);
  }

  toggleColumn(column: string): void {
    const index = this.displayedColumns.indexOf(column);
    if (index >= 0) {
      this.displayedColumns.splice(index, 1);
    } else {
      this.displayedColumns.push(column);
    }
  }

  getFilteredColumns(): string[] {
    if (!this.columnSearchText.trim()) return this.allColumns;
    const searchLower = this.columnSearchText.toLowerCase();
    return this.allColumns.filter(column =>
      this.columnDisplayNames[column].toLowerCase().includes(searchLower)
    );
  }

  downloadCSV(): void {
    const exportData = this.dataSource.data.map(item => ({
      LDAP: item.ldap,
      'Leave Type': item.leaveType,
      'Start Date': item.startDate,
      'End Date': item.endDate,
      Reason: item.reason,
      Status: item.status,
      Comment: item.comment
    }));

    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Leave History');

    const excelBuffer: any = XLSX.write(workbook, { bookType: 'csv', type: 'array' });
    const blob = new Blob([excelBuffer], { type: 'text/csv;charset=utf-8;' });
    saveAs(blob, `leave_history_${new Date().toISOString().split('T')[0]}.csv`);
  }

  editRequest(entry: any): void {
    if (entry.status !== 'PENDING') return;
    console.log("Entry ",entry);
    const dialogRef = this.dialog.open(EditLeaveRequestComponent, {
      width: '500px',
      data: { ...entry }
    });
  
    dialogRef.afterClosed().subscribe(updatedData => {
      if (updatedData) {
        console.log("Updated Data ",updatedData)
        const updatedRequest = { ...entry, ...updatedData };
        console.log("updated entry + updatedData ",updatedRequest);
        this.leaveService.updateLeaveRequest(updatedRequest).subscribe({
          next: () => {
            this.notificationService.showNotification({
              type: 'success',
              message: 'Leave request updated successfully.'
            });
            this.fetchLeaveHistory(this.ldap);
          },
          error: () => {
            this.notificationService.showNotification({
              type: 'error',
              message: 'Failed to update leave request.'
            });
          }
        });
      }
    });
  }
  

  deleteRequest(entry: any): void {
    if (entry.status !== 'PENDING') return;
  
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirm Deletion',
        message: `Are you sure you want to delete request from ${entry.startDate.toDateString()} to ${entry.endDate.toDateString()}?`,
        color: 'warn',
        confirmButtonText: 'Delete'
      }
    });
  
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.leaveService.deleteLeaveRequestById(entry.id).subscribe({
          next: () => {
            this.notificationService.showNotification({
              type: 'success',
              message: 'Leave request deleted successfully.'
            });
            this.fetchLeaveHistory(this.ldap); // Refresh list
          },
          error: () => {
            this.notificationService.showNotification({
              type: 'error',
              message: 'Failed to delete leave request.'
            });
          }
        });
      }
    });
  }
  

  get allColumnsSelected(): boolean {
    return this.displayedColumns.length === this.allColumns.length;
  }
}
