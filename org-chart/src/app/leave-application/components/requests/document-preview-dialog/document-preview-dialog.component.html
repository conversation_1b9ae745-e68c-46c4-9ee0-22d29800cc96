<mat-dialog-content>
    <p><strong>Reason:</strong> {{ data.reason }}</p>
  
    <div *ngIf="fullDocumentPath">
      <ng-container *ngIf="isImage(fullDocumentPath); else downloadOnly">
        <img [src]="fullDocumentPath"
             alt="Uploaded Document"
             style="max-width: 100%; max-height: 400px;">
        
        <br />
        <a [href]="fullDocumentPath"
           [attr.download]="getFilenameFromPath(fullDocumentPath)"
           target="_blank">
           📥 View/Download Image
        </a>
      </ng-container>
  
      <ng-template #downloadOnly>
        <a [href]="fullDocumentPath"
           [attr.download]="getFilenameFromPath(fullDocumentPath)"
           target="_blank">
           📥 Download Document
        </a>
      </ng-template>
    </div>
  </mat-dialog-content>
  