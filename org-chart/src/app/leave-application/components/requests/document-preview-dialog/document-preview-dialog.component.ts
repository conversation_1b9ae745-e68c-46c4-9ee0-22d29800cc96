import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';

@Component({
  selector: 'app-document-preview-dialog',
  templateUrl: './document-preview-dialog.component.html'
})
export class DocumentPreviewDialogComponent {
  fullDocumentPath: string;

  constructor(@Inject(MAT_DIALOG_DATA) public data: any) {
    // Make sure the path starts with / or full URL
    const baseUrl = 'https://teamsphere.in/'; 

    this.fullDocumentPath = data.documentPath?.startsWith('http')
      ? data.documentPath
      : baseUrl + data.documentPath;
  }

  isImage(path: string): boolean {
    return /\.(jpg|jpeg|png|gif)$/i.test(path);
  }
  getFilenameFromPath(path: string): string {
    return path?.split('/').pop() || 'document';
  }
  
}

