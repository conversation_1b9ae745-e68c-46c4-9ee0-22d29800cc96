import { Component, ElementRef, Input, ViewChild } from '@angular/core';
import { NotificationService } from 'src/app/shared/notification.service';
import { FormBuilder, FormGroup, Validators, FormControl } from '@angular/forms';
import { LeaveService } from 'src/app/services/leave.service';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from 'src/app/confirm-dialog/confirmation-dialog.component';


@Component({
  selector: 'app-balance',
  templateUrl: './balance.component.html',
  styleUrls: ['./balance.component.css']
})

export class BalanceComponent {
  @Input() leaveBalance: any;
  @Input() canUpload: boolean = false;
  @Input() userRole?: string;
  @ViewChild('fileInput') fileInput!: ElementRef;
  userName: string | undefined;


  constructor(private fb: FormBuilder, private notificationService: NotificationService, private leaveService: LeaveService, private dialog: MatDialog
  ) { }

  

  selectedFile: File | null = null;

  googleSheetId: string = '';

  ngOnInit() {
    this.fetchLeaveBalance();
  }

  fetchLeaveBalance(): void {
    this.userName = localStorage.getItem('username') || '';

    this.leaveService.getLeaveDetails(this.userName).subscribe({
      next: (data) => {
        this.leaveBalance.sick = data[0];
        this.leaveBalance.casual = data[1];
        this.leaveBalance.earned = data[2];
        this.leaveBalance.total = data[3];
        this.leaveBalance.totalwfh = data[4];
        this.leaveBalance.qtrwfh = data[5];
      },
      error: (error) => {
        console.error('Error fetching leave details:', error);
        this.notificationService.showNotification({
          type: 'error',
          message: 'Leave Balance not available for now.Please try later.'
        })
      }
    });
  }


  onFileSelected(event: any): void {
    const file: File = event.target.files[0];
  
    if (!file) return;
  
    const fileName = file.name.toLowerCase();
    if (!fileName.endsWith('.csv')) {
      this.notificationService.showNotification({
        type: 'error',
        message: 'Only CSV files are supported.'
      });
      return;
    }
  
    this.selectedFile = file;
  }
  
  uploadLeaveBalance(): void {
    if (!this.selectedFile) {
      this.notificationService.showNotification({
        type: 'error',
        message: 'Please select a file before uploading.'
      });
      return;
    }

    const formData = new FormData();
    formData.append('file', this.selectedFile);

    this.leaveService.uploadLeaveBalance(formData).subscribe({
      next: () => {
        this.notificationService.showNotification({
          type: 'success',
          message: 'Leave balances uploaded successfully!'
        });
        this.resetFileInput();
        this.fetchLeaveBalance();
      },
      error: (err) => {
        if (err.status === 409) {
          const rawMsg = err.error?.message || ''; 
        const match = rawMsg.match(/by\s+(\w+)/i);
        const uploader = match ? match[1] : 'someone';

          const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
            data: {
              title: 'Already Uploaded',
              message: `Leave balances already uploaded this month by ${uploader}. Do you want to re-upload?`,
              confirmButtonText: 'Re-upload',
              color: 'warn'
            }
          });

          dialogRef.afterClosed().subscribe(result => {
            if (result === true) {
              this.forceUploadLeaveBalance();
            }
          });
        } else {
          this.notificationService.showNotification({
            type: 'error',
            message: err.error?.message || 'Failed to upload leave balances.'
          });
        }
      }
    });
  }
  
  forceUploadLeaveBalance(): void {
    if (!this.selectedFile) return;
  
    const formData = new FormData();
    formData.append('file', this.selectedFile);
  
    this.leaveService.uploadLeaveBalance(formData, true).subscribe({
      next: () => {
        this.notificationService.showNotification({
        type: 'success',
        message: 'Leave balances re-uploaded successfully!'
      });
      this.fetchLeaveBalance();
    },
      error: () => this.notificationService.showNotification({
        type: 'error',
        message: 'Failed to re-upload leave balances.'
      })
      
    });
  }

  private resetFileInput(): void {
    this.selectedFile = null;
    if (this.fileInput && this.fileInput.nativeElement) {
      (this.fileInput.nativeElement as HTMLInputElement).value = '';
    }
  }

}