import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatRadioModule } from '@angular/material/radio';


@Component({
  selector: 'app-late-checkin-dialog',
  template: `
    <h1 mat-dialog-title>{{data.title}}</h1>
    <div mat-dialog-content>
    <p>{{ data.message }}</p>
      
      <mat-radio-group [(ngModel)]="selectedReason">
        <mat-radio-button *ngFor="let reason of reasons" [value]="reason">
          {{reason}}
        </mat-radio-button>
      </mat-radio-group>

      <mat-form-field appearance="outline" class="notes-field">
    <mat-label>Additional Notes</mat-label>
    <textarea matInput [(ngModel)]="notes" placeholder="If no reason applies, please comment here."></textarea>
</mat-form-field>
    </div>
    <div mat-dialog-actions>
  <button mat-button (click)="onCancel()">Cancel</button>
  <button mat-raised-button color="primary"
          (click)="onSubmit()"
          [disabled]="!selectedReason && !notes.trim()">Submit</button>
</div>

  `,
  styles: [`
    mat-radio-group {
      display: flex;
      flex-direction: column;
      margin: 15px 0;
    }
    mat-radio-button {
      margin: 5px 0;
    }
    .notes-field {
      width: 100%;
      margin-top: 15px;
    }
  `]
})
export class LateCheckinDialogComponent {
  reasons = [
    'Transport Issue (Cab got late)',
    'Personal Issue',
    'Medical Issue',
    'First Half Leave',
    'Second Half Leave',
    'Missed to fill attendance form (Present on floor)'
  ];
  selectedReason: string = '';
  notes: string = '';
  message: string = '';
  messageTitle: string = 'Check-In Reason Required';

  constructor(
    public dialogRef: MatDialogRef<LateCheckinDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) { }

  onCancel(): void {
    this.dialogRef.close();
  }


  ngOnInit(): void {
    if (this.data.isShiftMismatch) {
      this.messageTitle = 'Outside Shift Timing';
      this.message = `Your shift is ${this.data.shiftCode} (${this.data.shiftWindow}). You're marking attendance outside this window. Please provide a reason.`;
    } else {
      this.messageTitle = 'Late Check-In';
      this.message = `You are checking in late. Max login time is ${this.data.maxLoginTime}. Please provide a reason.`;
    }
  }

  onSubmit(): void {
    const result = {
      reason: this.selectedReason || 'Other',
      notes: this.notes
    };

    this.selectedReason = '';
    this.notes = '';

    this.dialogRef.close(result);
  }

}