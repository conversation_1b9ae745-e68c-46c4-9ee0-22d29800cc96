/* General Head<PERSON>ing */
.header {
  width: 100%;
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Logo Section Styling */
.logo-section {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.menu-toggle {
  display: none;
  margin-right: 10px;
}

.logo {
  display: flex;
  align-items: center;
}

svg {
  width: 60px;
  height: auto;
}

.dashboard-title {
  margin-left: 20px;
}

.dashboard-title h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
  color: #134472;
}

.dashboard-title p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

/* Navigation Styling */
.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  background-color: #134472;
  color: white;
}

.nav-items {
  display: flex;
  align-items: center;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  color: white;
  text-decoration: none;
  font-size: 14px;
  transition: background-color 0.3s;
}

.nav-link mat-icon {
  margin-right: 8px;
  font-size: 20px;
  height: 20px;
  width: 20px;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-link.active {
  background-color: rgba(255, 255, 255, 0.2);
  font-weight: 500;
}

/* User Actions */
.user-actions {
  display: flex;
  align-items: center;
}

.help-btn, .logout-btn {
  display: flex;
  align-items: center;
  color: white;
  margin-left: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.help-btn:hover, .logout-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.logout-btn {
  background-color: rgba(231, 80, 63, 0.2);
}

.logout-btn:hover {
  background-color: rgba(231, 80, 63, 0.3);
}

.help-btn mat-icon, .logout-btn mat-icon {
  margin-right: 8px;
}

/* Responsive Design */
@media (max-width: 992px) {
  .nav-items {
    overflow-x: auto;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
  }

  .nav-items::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
  }

  .nav-link span {
    display: none;
  }

  .nav-link mat-icon {
    margin-right: 0;
  }

  .help-btn span, .logout-btn span {
    display: none;
  }

  .help-btn mat-icon, .logout-btn mat-icon {
    margin-right: 0;
  }
}

@media (max-width: 768px) {
  .menu-toggle {
    display: block;
  }

  .dashboard-title {
    display: none;
  }

  .nav {
    padding: 0 10px;
  }

  .nav-link {
    padding: 12px 10px;
  }
}

/* Custom tooltip styles */
::ng-deep .mat-tooltip {
  background-color: rgba(0, 0, 0, 0.8);
  font-size: 14px;
  padding: 8px 12px;
  border-radius: 4px;
  margin: 8px;
}

.form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.form-container {
  position: relative;
  width: 480px; /* Fixed width to match Google Form */
  background: white;
  padding: 0; /* Remove padding */
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden; /* Hide overflow */
}

.close-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: none;
  background: #ff4444;
  color: white;
  font-size: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
}

.close-btn:hover {
  background: #cc0000;
}

/* Make iframe fit perfectly */
.form-container iframe {
  display: block; /* Remove inline spacing */
  width: 100%;
  height: 680px; /* Adjust height to match Google Form */
  border: none;
}
