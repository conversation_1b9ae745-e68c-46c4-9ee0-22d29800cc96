<div class="time-entry-form-container">
  <h2 mat-dialog-title>
    {{ isEditMode ? 'Edit Time Entry' : 'Add Time Entry' }}
  </h2>

  <!-- Toggle buttons container -->
  <div class="toggle-container">
    <!-- Toggle button for leads/managers -->
    <mat-slide-toggle
      *ngIf="isLeadOrManager"
      color="primary"
      [checked]="isOnBehalfMode"
      (change)="toggleOnBehalfMode()"
      [disabled]="isEditMode"
      class="toggle-button">
      {{ isOnBehalfMode ? 'On Behalf of Team Member' : 'My Time Entry' }}
    </mat-slide-toggle>

    <!-- Toggle button for overtime - only visible when FTech project is selected -->
    <mat-slide-toggle
      *ngIf="showOvertimeToggle"
      color="accent"
      [checked]="isOvertimeEntry"
      (change)="toggleOvertimeMode()"
      class="toggle-button">
      {{ isOvertimeEntry ? 'Overtime Entry' : 'Regular Entry' }}
    </mat-slide-toggle>
  </div>

  <form [formGroup]="timeEntryForm" (ngSubmit)="onSubmit()">
    <div mat-dialog-content>
      <div class="form-row">
        <!-- Date Worked -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Date Worked</mat-label>
          <input matInput [matDatepicker]="datePicker" formControlName="dateWorked" required>
          <mat-datepicker-toggle matSuffix [for]="datePicker"></mat-datepicker-toggle>
          <mat-datepicker #datePicker></mat-datepicker>
          <mat-error *ngIf="timeEntryForm.get('dateWorked')?.hasError('required')">
            Date Worked is required
          </mat-error>
        </mat-form-field>

        <!-- User ID Field -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>User ID</mat-label>
          <input matInput formControlName="ldap" required [readonly]="!isOnBehalfMode">
          <mat-error *ngIf="timeEntryForm.get('ldap')?.hasError('required')">
            User ID is required
          </mat-error>
        </mat-form-field>
      </div>

      <div class="form-row">
        <!-- Reporting Senior's UserID -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Reporting Senior's UserID</mat-label>
          <input matInput formControlName="reportingSeniorUserId" required>
          <mat-error *ngIf="timeEntryForm.get('reportingSeniorUserId')?.hasError('required')">
            Reporting Senior's UserID is required
          </mat-error>
        </mat-form-field>

        <!-- Resource Name -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Resource Name</mat-label>
          <input matInput formControlName="resourceName" placeholder="lastname, firstname">
        </mat-form-field>
      </div>

      <div class="form-row">
        <!-- Company -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Company</mat-label>
          <input matInput formControlName="company" value="ABC" readonly>
        </mat-form-field>

        <!-- Type -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Type</mat-label>
          <mat-select formControlName="type" required>
            <mat-option value="Ticket">Ticket</mat-option>
            <mat-option value="Meeting">Meeting</mat-option>
            <mat-option value="Training">Training</mat-option>
            <mat-option value="Other">Other</mat-option>
          </mat-select>
          <mat-error *ngIf="timeEntryForm.get('type')?.hasError('required')">
            Type is required
          </mat-error>
        </mat-form-field>
      </div>

      <div class="form-row">
        <!-- Hours Worked -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Hours Worked</mat-label>
          <input matInput type="number" min="0.1" max="8" step="0.1" formControlName="hoursWorked" required>
          <mat-error *ngIf="timeEntryForm.get('hoursWorked')?.hasError('required')">
            Hours Worked is required
          </mat-error>
          <mat-error *ngIf="timeEntryForm.get('hoursWorked')?.hasError('min')">
            Hours must be at least 0.1
          </mat-error>
          <mat-error *ngIf="timeEntryForm.get('hoursWorked')?.hasError('max')">
            Hours cannot exceed 8
          </mat-error>
        </mat-form-field>

        <!-- Billable Hours Worked -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Billable Hours Worked</mat-label>
          <input matInput type="number" min="0" max="8" step="0.1" formControlName="billableHoursWorked" required>
          <mat-error *ngIf="timeEntryForm.get('billableHoursWorked')?.hasError('required')">
            Billable Hours Worked is required
          </mat-error>
          <mat-error *ngIf="timeEntryForm.get('billableHoursWorked')?.hasError('min')">
            Billable hours must be at least 0
          </mat-error>
          <mat-error *ngIf="timeEntryForm.get('billableHoursWorked')?.hasError('max')">
            Billable hours cannot exceed 8
          </mat-error>
        </mat-form-field>
      </div>

      <div class="form-row">
        <!-- Notes -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Notes</mat-label>
          <textarea matInput formControlName="notes" rows="3" placeholder="Enter any notes about this time entry"></textarea>
        </mat-form-field>
      </div>

      <div class="form-row">
        <!-- Internal Notes -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Internal Notes</mat-label>
          <textarea matInput formControlName="internalNotes" rows="2" placeholder="Internal notes (not visible to client)"></textarea>
        </mat-form-field>
      </div>
    </div>

    <div mat-dialog-actions align="end">
      <button mat-button type="button" (click)="onCancel()">Cancel</button>
      <button mat-raised-button color="primary" type="submit" [disabled]="timeEntryForm.invalid">
        {{ isEditMode ? 'Update' : 'Submit' }}
      </button>
    </div>
  </form>
</div>
