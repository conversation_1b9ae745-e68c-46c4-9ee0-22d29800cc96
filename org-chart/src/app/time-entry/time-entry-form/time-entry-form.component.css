.time-entry-form-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.full-width {
  width: 100%;
}

mat-form-field {
  width: 100%;
}

mat-dialog-actions {
  margin-top: 20px;
  padding: 16px 0;
}

textarea {
  min-height: 80px;
}

mat-select {
  width: 100%;
}

/* Error message styling */
mat-error {
  font-size: 12px;
  margin-top: 4px;
}

/* Date picker styling */
.mat-datepicker-toggle {
  color: rgba(0, 0, 0, 0.54);
}

/* Select dropdown styling */
.mat-select-panel {
  max-height: 350px;
}

/* Responsive layout */
@media (max-width: 600px) {
  .form-row {
    flex-direction: column;
    gap: 8px;
  }

  .time-entry-form-container {
    padding: 10px;
  }
}

/* Style for the dialog title */
h2.mat-dialog-title {
  margin-top: 0;
  color: #333;
  font-weight: 500;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
  margin-bottom: 10px;
}

/* Toggle container styling */
.toggle-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 0 10px;
  flex-wrap: wrap;
}

/* Style for the toggle button */
.mat-slide-toggle {
  font-weight: 500;
}

/* Add margin between toggle buttons */
.toggle-button {
  margin-right: 16px;
}

/* Style for overtime toggle */
.mat-slide-toggle.mat-accent.mat-checked .mat-slide-toggle-thumb {
  background-color: #ff4081; /* Pink accent color for overtime */
}

.mat-slide-toggle.mat-checked .mat-slide-toggle-thumb {
  background-color: #005cbf;
}

/* Animation for overtime toggle appearance */
mat-slide-toggle[color="accent"] {
  animation: fadeIn 0.5s ease-in-out;
  border-radius: 4px;
  padding: 4px 8px;
  background-color: rgba(255, 64, 129, 0.1); /* Light pink background */
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Style for the submit button */
button[type="submit"] {
  background-color: #fff !important;
  color: #005cbf !important;
}

button[type="submit"]:hover:not([disabled]) {
  background-color: #005cbf !important;
  color: #fff !important;
}

button[type="submit"]:disabled {
  background-color: #f5f5f5 !important;
  color: #bdbdbd !important;
}

/* Style for the cancel button */
button[type="button"] {
  color: #757575;
}

button[type="button"]:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

/* Style for Ops Excellence project indicator in dropdown */
.ftech-indicator {
  color: #ff4081;
  font-weight: bold;
  margin-left: 4px;
}

/* Style for overtime hint */
.overtime-hint {
  color: #ff4081;
  font-size: 12px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}
