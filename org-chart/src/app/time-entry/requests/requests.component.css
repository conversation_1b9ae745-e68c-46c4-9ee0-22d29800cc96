.requests-dashboard {
  padding: 20px;
}

.actions {
  display: flex;
  gap: 16px;
  align-items: center;
  padding: 16px 20px;
  background: white;
  border-radius: 4px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-wrap: wrap;
}

.actions button {
  margin-right: 10px;
}

.mat-elevation-z8 {
  width: 100%;
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  border-radius: 4px;
}

/* Add spacing between buttons */
.action-button {
  margin-right: 8px; /* Space between buttons */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Remove margin from the last button */
.action-button:last-child {
  margin-right: 0;
}

/* Style for the search field */
.search-field {
  width: 300px;
  margin: 0 !important;
}

.search-field input {
  height: 10px; /* Control the height of the input box inside the field */
  padding: 4px 8px; /* Adjust padding for better fit */
  font-size: 14px; /* Adjust font size for better readability */
}

/* General <PERSON><PERSON>ver Styles */
.mat-raised-button:hover {
  background-color: #9e9e9e !important; /* Darker grey */
  color: #fff !important; /* White text for contrast */
}

/* Primary Button Hover */
.mat-raised-button[color="primary"]:hover {
  background-color: #9e9e9e !important; /* Darker grey */
  color: #fff !important; /* White text for contrast */
}

/* Action Button */
.action-button {
  background-color: #fff !important; /* Light blue */
  color: #005cbf !important; /* Dark blue text */
}

.action-button:hover {
  background-color: #005cbf !important; /* Dark blue */
  color: #fff !important; /* White text */
}

/* Search Field Icon Styling */
.search-field mat-icon {
  color: #757575 !important; /* Dark grey for icon */
}

/* Table styles */
.table-responsive {
  margin: 20px;
  overflow-x: auto;
  width: calc(100% - 40px); /* Account for margins */
  position: relative;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Ensure the table takes up the full width of its content */
.table-responsive .mat-table {
  min-width: 100%;
  width: max-content; /* Allow table to expand based on content */
}

/* Add a subtle horizontal scrollbar indicator */
.table-responsive:after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 50px;
  height: 4px;
  background: linear-gradient(to right, transparent, rgba(103, 58, 183, 0.3));
  border-radius: 0 0 4px 0;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* Show the scroll indicator when the table is scrollable */
.table-responsive.scrollable:after {
  opacity: 1;
}

/* Improved table cell styling */
mat-header-cell, mat-cell {
  padding: 12px 16px !important;
  font-size: 14px;
}

/* Column widths */
.mat-column-select {
  min-width: 60px;
  max-width: 60px;
}

.mat-column-id {
  min-width: 80px;
  max-width: 100px;
}

.mat-column-userName {
  min-width: 150px;
}

.mat-column-projectName {
  min-width: 180px;
}

.mat-column-date {
  min-width: 120px;
}

.mat-column-hours {
  min-width: 100px;
  text-align: right;
}

.mat-column-description {
  min-width: 200px;
}

.mat-column-process {
  min-width: 120px;
}

.mat-column-shift {
  min-width: 100px;
}

.mat-column-isOvertime {
  min-width: 100px;
  text-align: center;
}

.mat-column-status {
  min-width: 120px;
}

.mat-column-comments {
  min-width: 200px;
}

.mat-column-actions {
  min-width: 100px;
  max-width: 100px;
  text-align: right;
}

/* Header container for filter inputs */
.header-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.header-text {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.87);
  margin-right: 8px;
}

/* Filter fields styling */
.filter-field,
.date-field {
  min-width: 200px;
  margin: 0 !important;
}

.filter-select {
  width: 90%;
  font-size: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 4px;
  background-color: white;
  margin-top: 4px;
}

/* Status styling */
.status-pending {
  color: #e65100;
  background-color: #fff3e0;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.status-approved {
  color: #2e7d32;
  background-color: #e8f5e9;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.status-rejected {
  color: #c62828;
  background-color: #ffebee;
  padding: 4px 12px;
  border-border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

/* Cell styling */
mat-cell {
  padding: 8px 0;
}

/* Action buttons in cells */
mat-cell button {
  margin-right: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

mat-cell button:last-child {
  margin-right: 0;
}

/* Description cell with ellipsis */
.description-cell, .comments-cell {
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.no-comments {
  color: #9e9e9e;
  font-style: italic;
}

.status-processed {
  color: #9e9e9e;
  font-style: italic;
  font-size: 0.9em;
}

/* Ensure consistent row height */
mat-row, mat-header-row {
  min-height: 48px !important;
}

/* Add zebra striping for better readability */
mat-row:nth-child(even) {
  background-color: rgba(0, 0, 0, 0.02);
}

/* Hover effect on rows */
mat-row:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

/* Improve paginator styling */
mat-paginator {
  background-color: transparent;
  border-top: 1px solid #e0e0e0;
}

/* Date range container */
.date-range-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* Apply button styling */
.apply-button {
  background-color: #673ab7 !important;
  color: white !important;
  height: 36px;
  padding: 0 16px !important;
  font-weight: 500;
  border-radius: 4px;
  margin-top: 0 !important;
}

.apply-button:hover {
  background-color: #5e35b1 !important;
}

/* Back button styling */
.back-button {
  background-color: #673ab7 !important;
  color: white !important;
  height: 36px;
  padding: 0 16px !important;
  font-weight: 500;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.back-button:hover {
  background-color: #5e35b1 !important;
}

/* Filter inputs */
.filter-input {
  width: 100%;
  font-size: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 4px 8px;
  margin-top: 4px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  margin: 0 16px;
}

.action-buttons button {
  display: flex;
  align-items: center;
  gap: 4px;
}

.action-buttons button[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

.mat-column-select {
  width: 48px;
  padding: 0 8px;
}

.mat-checkbox {
  margin: 0;
}

.action-button.download-button {
  background-color: #fff !important;
  color: #e67e22 !important;
}

.action-button.download-button:hover {
  background-color: #e67e22 !important;
  color: #fff !important;
}

/* Toggle Filter Button Styling */
.toggle-filter-button {
  background-color: #673ab7 !important;
  color: white !important;
  height: 36px;
  padding: 0 16px !important;
  font-weight: 500;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.toggle-filter-button:hover {
  background-color: #5e35b1 !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(103, 58, 183, 0.3);
}

.toggle-filter-button mat-icon {
  font-size: 18px;
  height: 18px;
  width: 18px;
}

/* Highlight rows with insufficient day time */
.insufficient-day-time {
  background-color: rgba(255, 0, 0, 0.15) !important;
}

.insufficient-day-time:hover {
  background-color: rgba(255, 0, 0, 0.25) !important;
}

/* Overtime icon styling */
.mat-column-isOvertime .mat-icon {
  font-size: 18px;
  height: 18px;
  width: 18px;
  line-height: 18px;
}

.mat-column-isOvertime .mat-icon[color="accent"] {
  color: #ff4081;
}

.mat-column-isOvertime .mat-icon[color="disabled"] {
  color: #bdbdbd;
}



/* Column toggle menu styles */
.column-toggle-button {
  background-color: #673ab7 !important;
  color: white !important;
  height: 36px;
  padding: 0 16px !important;
  font-weight: 500;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.column-toggle-button:hover {
  background-color: #5e35b1 !important;
}

.column-menu-content {
  padding: 16px;
  min-width: 250px;
}

.column-menu-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
  color: #673ab7;
}

.column-search-field {
  width: 100%;
  margin-top: 8px;
  margin-bottom: 8px;
}

.select-all-container {
  margin: 12px 0;
}

.select-all-checkbox {
  font-weight: 500;
}

.column-menu-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 16px;
  max-height: 300px;
  overflow-y: auto;
  padding-right: 8px;
}

.column-toggle-checkbox {
  margin: 8px 0;
}

/* Scrollbar styling for the column menu */
.column-menu-items::-webkit-scrollbar {
  width: 6px;
}

.column-menu-items::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.column-menu-items::-webkit-scrollbar-thumb {
  background: #673ab7;
  border-radius: 3px;
}

.column-menu-items::-webkit-scrollbar-thumb:hover {
  background: #5e35b1;
}



/* Info button styling */
.info-button {
  width: 24px;
  height: 24px;
  line-height: 24px;
  margin-left: 8px;
  padding: 0;
  min-width: unset;
  vertical-align: middle;
}

.info-icon {
  color: #f44336;
  font-size: 18px;
  height: 18px;
  width: 18px;
  line-height: 18px;
}

/* Column filter styles */

/* Filter menu styles */
.filter-menu-content {
  padding: 16px;
  min-width: 250px;
  max-width: 300px;
}

.filter-search {
  width: 100%;
  margin-bottom: 8px;
}

.filter-menu-content hr {
  margin: 8px 0;
  border: none;
  border-top: 1px solid #e0e0e0;
}

.filter-menu-content mat-checkbox {
  display: block;
  margin: 8px 0;
}
