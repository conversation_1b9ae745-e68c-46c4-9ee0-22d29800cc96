.week-copy-dialog-container {
  padding: 16px;
}

.source-entry-info {
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
}

.info-row {
  display: flex;
  margin-bottom: 8px;
}

.label {
  font-weight: 500;
  width: 120px;
}

.form-row {
  margin-bottom: 16px;
}

.full-width {
  width: 100%;
}

.weekdays-selection {
  margin-top: 16px;
}

.weekday-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.weekday-item {
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.weekday-item:hover:not(.disabled) {
  background-color: #f0f0f0;
}

.weekday-item.selected:not(.disabled) {
  background-color: #e3f2fd;
}

.weekday-item.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.source-indicator {
  margin-left: 8px;
  font-size: 0.8em;
  color: #666;
}

.time-filled-indicator {
  margin-left: 8px;
  font-size: 0.8em;
  color: #f44336;
}

.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  gap: 12px;
}

.loading-indicator span {
  color: #666;
}

mat-dialog-actions {
  margin-top: 16px;
}
