<div class="time-entry-dashboard">
  <app-page-header
  title="Time Entries"
  subtitle="Manage and track your time entries across projects">
</app-page-header>


  <div class="actions">
    <button mat-raised-button color="primary" class="action-button add-button" (click)="openAddTimeEntryForm()">
      <mat-icon>add</mat-icon>Add Time Entry
    </button>

    <button mat-raised-button color="primary" class="action-button" (click)="viewProjects()"
      *ngIf="isLeadOrManager()" matTooltip="View and manage projects">
      <mat-icon>folder</mat-icon>View Projects
    </button>

    <button mat-raised-button color="primary" class="action-button" (click)="viewRequests()"
      *ngIf="isLeadOrManager()" matTooltip="View team time entry requests">
      <mat-icon>assignment</mat-icon>Requests
    </button>

    <button mat-raised-button color="primary" class="action-button" (click)="assignProjects()"
      *ngIf="isLeadOrManager()" matTooltip="Assign projects to team members">
      <mat-icon>group_add</mat-icon>Assign Projects
    </button>

    <button mat-raised-button color="primary" class="action-button download-button" (click)="downloadCSV()" matTooltip="Download time entries as CSV">
      <mat-icon>download</mat-icon> Download CSV
    </button>

    <!-- Show Column Filters Button -->
    <button mat-raised-button (click)="toggleColumnFilters()" class="action-button toggle-filter-button">
      <mat-icon>{{ showColumnFilters ? 'visibility_off' : 'filter_list' }}</mat-icon>
      {{ showColumnFilters ? 'Hide' : 'Show' }} Column Filters
    </button>

    <!-- Column Toggle Button -->
    <button mat-raised-button [matMenuTriggerFor]="columnMenu" class="column-toggle-button">
      <mat-icon>view_column</mat-icon>
      Toggle Columns
    </button>

    <!-- Column Toggle Menu -->
    <mat-menu #columnMenu="matMenu" class="column-menu">
      <div class="column-menu-content" (click)="$event.stopPropagation()">
        <h3 class="column-menu-title">Toggle Columns</h3>
        <mat-divider></mat-divider>

        <!-- Search input -->
        <mat-form-field appearance="outline" class="column-search-field">
          <mat-label>Search columns</mat-label>
          <input matInput [(ngModel)]="columnSearchText" placeholder="Search columns">
          <button *ngIf="columnSearchText" matSuffix mat-icon-button aria-label="Clear" (click)="columnSearchText=''">
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>

        <!-- Select All checkbox -->
        <div class="select-all-container">
          <mat-checkbox
            [checked]="allColumnsSelected"
            (change)="toggleAllColumns($event.checked)"
            class="select-all-checkbox">
            Select All
          </mat-checkbox>
        </div>

        <mat-divider></mat-divider>

        <!-- Column checkboxes -->
        <div class="column-menu-items">
          <mat-checkbox
            *ngFor="let column of getFilteredColumns()"
            [checked]="isColumnDisplayed(column)"
            (change)="toggleColumn(column)"
            [disabled]="column === 'actions'"
            class="column-toggle-checkbox">
            {{ columnDisplayNames[column] }}
          </mat-checkbox>
        </div>
      </div>
    </mat-menu>

    <div class="date-range-container">
      <mat-form-field appearance="outline">
        <mat-label>Date Range</mat-label>
        <mat-date-range-input [formGroup]="dateRange" [rangePicker]="picker">
          <input matStartDate formControlName="start" placeholder="Start date">
          <input matEndDate formControlName="end" placeholder="End date">
        </mat-date-range-input>
        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
        <mat-date-range-picker #picker></mat-date-range-picker>
      </mat-form-field>
      <button mat-raised-button color="primary" (click)="applyDateFilter()" class="apply-filter-btn">
        <mat-icon>filter_list</mat-icon> Apply
      </button>
    </div>

    <!-- Global Search Field -->
    <mat-form-field appearance="outline" class="search-field">
      <mat-label>Search</mat-label>
      <mat-icon matPrefix>search</mat-icon>
      <input matInput (keyup)="applyGlobalFilter($event)" placeholder="Search time entries">
    </mat-form-field>
  </div>

  <div class="table-responsive">
    <div class="table-scroll-container">
      <mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8">
      <!-- Date Worked Column -->
      <ng-container matColumnDef="dateWorked">
        <mat-header-cell *matHeaderCellDef mat-sort-header>
          <div class="header-container">
            <span class="header-text">Date Worked</span>
            <button mat-icon-button #dateWorkedTrigger="matMenuTrigger"
                    *ngIf="showColumnFilters"
                    [matMenuTriggerFor]="filterMenu"
                    (menuOpened)="openFilterMenu('dateWorked', dateWorkedTrigger)"
                    [color]="isFilterActive('dateWorked') ? 'accent' : ''"
                    (click)="$event.stopPropagation()">
              <mat-icon>filter_list</mat-icon>
            </button>
          </div>
        </mat-header-cell>
        <mat-cell *matCellDef="let entry">{{ entry.dateWorked || entry.date || entry.entryDate | date: 'yyyy-MM-dd' }}</mat-cell>
      </ng-container>

      <!-- User ID Column -->
      <ng-container matColumnDef="userIdField">
        <mat-header-cell *matHeaderCellDef mat-sort-header>
          <div class="header-container">
            <span class="header-text">User ID</span>
            <button mat-icon-button #userIdFieldTrigger="matMenuTrigger"
                    *ngIf="showColumnFilters"
                    [matMenuTriggerFor]="filterMenu"
                    (menuOpened)="openFilterMenu('userIdField', userIdFieldTrigger)"
                    [color]="isFilterActive('userIdField') ? 'accent' : ''"
                    (click)="$event.stopPropagation()">
              <mat-icon>filter_list</mat-icon>
            </button>
          </div>
        </mat-header-cell>
        <mat-cell *matCellDef="let entry">{{ entry.userIdField || entry.ldap }}</mat-cell>
      </ng-container>

      <!-- Reporting Senior's UserID Column -->
      <ng-container matColumnDef="reportingSeniorUserId">
        <mat-header-cell *matHeaderCellDef mat-sort-header>
          <div class="header-container">
            <span class="header-text">Reporting Senior's UserID</span>
            <button mat-icon-button #reportingSeniorUserIdTrigger="matMenuTrigger"
                    *ngIf="showColumnFilters"
                    [matMenuTriggerFor]="filterMenu"
                    (menuOpened)="openFilterMenu('reportingSeniorUserId', reportingSeniorUserIdTrigger)"
                    [color]="isFilterActive('reportingSeniorUserId') ? 'accent' : ''"
                    (click)="$event.stopPropagation()">
              <mat-icon>filter_list</mat-icon>
            </button>
          </div>
        </mat-header-cell>
        <mat-cell *matCellDef="let entry">{{ entry.reportingSeniorUserId || entry.leadUsername }}</mat-cell>
      </ng-container>

      <!-- Resource Name Column -->
      <ng-container matColumnDef="resourceName">
        <mat-header-cell *matHeaderCellDef mat-sort-header>
          <div class="header-container">
            <span class="header-text">Resource Name</span>
            <button mat-icon-button #resourceNameTrigger="matMenuTrigger"
                    *ngIf="showColumnFilters"
                    [matMenuTriggerFor]="filterMenu"
                    (menuOpened)="openFilterMenu('resourceName', resourceNameTrigger)"
                    [color]="isFilterActive('resourceName') ? 'accent' : ''"
                    (click)="$event.stopPropagation()">
              <mat-icon>filter_list</mat-icon>
            </button>
          </div>
        </mat-header-cell>
        <mat-cell *matCellDef="let entry">{{ entry.resourceName }}</mat-cell>
      </ng-container>

      <!-- Company Column -->
      <ng-container matColumnDef="company">
        <mat-header-cell *matHeaderCellDef mat-sort-header>
          <div class="header-container">
            <span class="header-text">Company</span>
            <button mat-icon-button #companyTrigger="matMenuTrigger"
                    *ngIf="showColumnFilters"
                    [matMenuTriggerFor]="filterMenu"
                    (menuOpened)="openFilterMenu('company', companyTrigger)"
                    [color]="isFilterActive('company') ? 'accent' : ''"
                    (click)="$event.stopPropagation()">
              <mat-icon>filter_list</mat-icon>
            </button>
          </div>
        </mat-header-cell>
        <mat-cell *matCellDef="let entry">{{ entry.company || 'ABC' }}</mat-cell>
      </ng-container>

      <!-- Type Column -->
      <ng-container matColumnDef="type">
        <mat-header-cell *matHeaderCellDef mat-sort-header>
          <div class="header-container">
            <span class="header-text">Type</span>
            <button mat-icon-button #typeTrigger="matMenuTrigger"
                    *ngIf="showColumnFilters"
                    [matMenuTriggerFor]="filterMenu"
                    (menuOpened)="openFilterMenu('type', typeTrigger)"
                    [color]="isFilterActive('type') ? 'accent' : ''"
                    (click)="$event.stopPropagation()">
              <mat-icon>filter_list</mat-icon>
            </button>
          </div>
        </mat-header-cell>
        <mat-cell *matCellDef="let entry">{{ entry.type || entry.activity || 'Ticket' }}</mat-cell>
      </ng-container>

      <!-- Hours Worked Column -->
      <ng-container matColumnDef="hoursWorked">
        <mat-header-cell *matHeaderCellDef mat-sort-header>
          <div class="header-container">
            <span class="header-text">Hours Worked</span>
            <button mat-icon-button #hoursWorkedTrigger="matMenuTrigger"
                    *ngIf="showColumnFilters"
                    [matMenuTriggerFor]="filterMenu"
                    (menuOpened)="openFilterMenu('hoursWorked', hoursWorkedTrigger)"
                    [color]="isFilterActive('hoursWorked') ? 'accent' : ''"
                    (click)="$event.stopPropagation()">
              <mat-icon>filter_list</mat-icon>
            </button>
          </div>
        </mat-header-cell>
        <mat-cell *matCellDef="let entry">{{ entry.hoursWorked || (entry.timeInMins ? (entry.timeInMins / 60).toFixed(2) : '0') }}</mat-cell>
      </ng-container>

      <!-- Billable Hours Worked Column -->
      <ng-container matColumnDef="billableHoursWorked">
        <mat-header-cell *matHeaderCellDef mat-sort-header>
          <div class="header-container">
            <span class="header-text">Billable Hours Worked</span>
            <button mat-icon-button #billableHoursWorkedTrigger="matMenuTrigger"
                    *ngIf="showColumnFilters"
                    [matMenuTriggerFor]="filterMenu"
                    (menuOpened)="openFilterMenu('billableHoursWorked', billableHoursWorkedTrigger)"
                    [color]="isFilterActive('billableHoursWorked') ? 'accent' : ''"
                    (click)="$event.stopPropagation()">
              <mat-icon>filter_list</mat-icon>
            </button>
          </div>
        </mat-header-cell>
        <mat-cell *matCellDef="let entry">{{ entry.billableHoursWorked || (entry.timeInMins ? (entry.timeInMins / 60).toFixed(2) : '0') }}</mat-cell>
      </ng-container>


      <!-- Notes Column -->
      <ng-container matColumnDef="notes">
        <mat-header-cell *matHeaderCellDef mat-sort-header>
          <div class="header-container">
            <span class="header-text">Notes</span>
            <button mat-icon-button #notesTrigger="matMenuTrigger"
                    *ngIf="showColumnFilters"
                    [matMenuTriggerFor]="filterMenu"
                    (menuOpened)="openFilterMenu('notes', notesTrigger)"
                    [color]="isFilterActive('notes') ? 'accent' : ''"
                    (click)="$event.stopPropagation()">
              <mat-icon>filter_list</mat-icon>
            </button>
          </div>
        </mat-header-cell>
        <mat-cell *matCellDef="let entry" class="comment-cell" [matTooltip]="entry.notes || entry.comment || '-'">{{ entry.notes || entry.comment || '-' }}</mat-cell>
      </ng-container>

      <!-- Internal Notes Column -->
      <ng-container matColumnDef="internalNotes">
        <mat-header-cell *matHeaderCellDef mat-sort-header>
          <div class="header-container">
            <span class="header-text">Internal Notes</span>
            <button mat-icon-button #internalNotesTrigger="matMenuTrigger"
                    *ngIf="showColumnFilters"
                    [matMenuTriggerFor]="filterMenu"
                    (menuOpened)="openFilterMenu('internalNotes', internalNotesTrigger)"
                    [color]="isFilterActive('internalNotes') ? 'accent' : ''"
                    (click)="$event.stopPropagation()">
              <mat-icon>filter_list</mat-icon>
            </button>
          </div>
        </mat-header-cell>
        <mat-cell *matCellDef="let entry" class="comment-cell" [matTooltip]="entry.internalNotes || '-'">{{ entry.internalNotes || '-' }}</mat-cell>
      </ng-container>

      <!-- Status Column -->
      <ng-container matColumnDef="status">
        <mat-header-cell *matHeaderCellDef mat-sort-header>
          <div class="header-container">
            <span class="header-text">Status</span>
            <button mat-icon-button #statusTrigger="matMenuTrigger"
                    *ngIf="showColumnFilters"
                    [matMenuTriggerFor]="filterMenu"
                    (menuOpened)="openFilterMenu('status', statusTrigger)"
                    [color]="isFilterActive('status') ? 'accent' : ''"
                    (click)="$event.stopPropagation()">
              <mat-icon>filter_list</mat-icon>
            </button>
          </div>
        </mat-header-cell>
        <mat-cell *matCellDef="let entry">
          <div class="status-cell">
            <span [ngClass]="{
              'status-pending': entry.status === 'PENDING',
              'status-approved': entry.status === 'APPROVED',
              'status-rejected': entry.status === 'REJECTED'
            }">
              {{ entry.status }}
              <mat-icon *ngIf="entry.status === 'APPROVED'" class="verified-icon">verified</mat-icon>
            </span>
            <button *ngIf="entry.status === 'REJECTED' && (entry.comments || entry.comment || entry.rejectionComment)"
                    mat-icon-button
                    color="primary"
                    class="view-comment-btn"
                    (click)="viewRejectionComment(entry)">
              <mat-icon>comment</mat-icon>
            </button>
          </div>
        </mat-cell>
      </ng-container>

      <!-- Actions Column -->
      <ng-container matColumnDef="actions">
        <mat-header-cell *matHeaderCellDef>Actions</mat-header-cell>
        <mat-cell *matCellDef="let entry" class="actions-cell">
          <!-- Clone button -->
          <button mat-icon-button color="primary" (click)="cloneTimeEntry(entry)"
                  matTooltip="Clone Time Entry"
                  matTooltipPosition="above"
                  class="action-btn">
            <mat-icon>content_copy</mat-icon>
          </button>

          <!-- Copy to Week button -->
          <button mat-icon-button color="accent" (click)="copyToWeek(entry)"
                  matTooltip="Copy to Week"
                  matTooltipPosition="above"
                  class="action-btn">
            <mat-icon>date_range</mat-icon>
          </button>

          <!-- 3-dot menu button -->
          <button mat-icon-button [matMenuTriggerFor]="actionsMenu"
                  aria-label="More actions"
                  matTooltip="More actions"
                  matTooltipPosition="above"
                  class="action-btn">
            <mat-icon>more_vert</mat-icon>
          </button>

          <!-- Actions menu (only Edit and Delete) -->
          <mat-menu #actionsMenu="matMenu" class="actions-menu">
            <button mat-menu-item (click)="editTimeEntry(entry)"
                    [disabled]="entry.status === 'APPROVED'"
                    matTooltip="Edit this time entry"
                    [class.disabled-menu-item]="entry.status === 'APPROVED'">
              <mat-icon color="primary">edit</mat-icon>
              <span>Edit Time Entry</span>
            </button>

            <mat-divider></mat-divider>

            <button mat-menu-item (click)="deleteTimeEntry(entry)"
                    [disabled]="entry.status === 'APPROVED'"
                    matTooltip="Delete this time entry"
                    class="delete-menu-item"
                    [class.disabled-menu-item]="entry.status === 'APPROVED'">
              <mat-icon color="warn">delete</mat-icon>
              <span>Delete Time Entry</span>
            </button>
          </mat-menu>
        </mat-cell>
      </ng-container>

      <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
      <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
    </mat-table>
    </div>

    <mat-paginator
      [length]="dataSource.data.length"
      [pageSize]="10"
      [pageSizeOptions]="[10, 15, 20, 50]"
      showFirstLastButtons>
    </mat-paginator>
  </div>
</div>

<!-- Filter Menu Template -->
<mat-menu #filterMenu="matMenu" class="filter-menu">
  <div class="filter-menu-content" (click)="$event.stopPropagation()">
    <ng-container *ngIf="currentFilterMenuState.columnKey">
      <!-- Search Input -->
      <mat-form-field appearance="outline" class="filter-search">
        <mat-label>Search</mat-label>
        <input matInput [(ngModel)]="currentFilterMenuState.searchText" placeholder="Search options">
      </mat-form-field>

      <!-- Select All Checkbox -->
      <mat-checkbox
        [checked]="isAllTempSelected()"
        [indeterminate]="isSomeTempSelected()"
        (change)="toggleSelectAllTemp($event.checked)">
        Select All ({{ getUniqueColumnValues(currentFilterMenuState.columnKey).length }} items)
      </mat-checkbox>
      <hr>

      <!-- Filter Options -->
      <div style="max-height: 200px; overflow-y: auto;">
        <mat-checkbox *ngFor="let value of filteredMenuOptions"
          [checked]="isTempSelected(value)"
          (change)="toggleTempSelection(value, $event.checked)">
          {{ value }}
        </mat-checkbox>
      </div>
      <hr>

      <!-- Action Buttons -->
      <div style="display: flex; justify-content: space-between; margin-top: 10px;">
        <button mat-button (click)="onFilterApplied()">Apply</button>
        <button mat-button (click)="clearColumnFilter()">Clear</button>
      </div>
    </ng-container>
  </div>
</mat-menu>
