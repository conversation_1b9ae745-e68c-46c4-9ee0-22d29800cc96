<div class="password-reset-container">
  <svg class="logo" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 441.85 137.77">
    <defs>
      <style>
        .ac0b5b28-f94c-4720-9e2e-71e4531b0b17 {
          fill: #134472;
        }
        .b74da1e6-a396-4573-8b65-1f5ed9787516 {
          fill: #f8b641;
        }
        .a8886bc2-bba6-4f5c-965b-c6c6d5787d09 {
          fill: #f47b34;
        }
      </style>
    </defs>
    <g id="fbc26f45-ae69-49ca-bbc8-efdab82724ee" data-name="Layer 2">
      <g id="a7459c6a-6262-4fbe-ac22-0bf0bb57d90c" data-name="vaco">
        <polygon class="ac0b5b28-f94c-4720-9e2e-71e4531b0b17" points="354.48 115.89 354.71 116.06 354.71 115.89 354.48 115.89"></polygon>
        <path class="b74da1e6-a396-4573-8b65-1f5ed9787516" d="M69.28,0c.65,0,1.26,0,1.91,0l-.1,2.05L69.83,26.47l-.37,7.32-2.7,48.66L66,96a65.43,65.43,0,0,0-9.41-3l1.69-39.53-15.76,23-9.73,14A126.5,126.5,0,0,0,4.65,94.17,68.22,68.22,0,0,1,0,69.28,69.28,69.28,0,0,1,69.28,0"></path>
        <path class="a8886bc2-bba6-4f5c-965b-c6c6d5787d09" d="M77.9.59a69.24,69.24,0,0,1,42.6,115.3,10.63,10.63,0,0,1-.94,1Z"></path>
        <path class="ac0b5b28-f94c-4720-9e2e-71e4531b0b17" d="M93.68,133a67.71,67.71,0,0,1-7.23,2.32c-.07,0-.15.07-.22.07-12.6,3.72-28.84,2.14-33.69,1-.44-.1-.74-.15-.74-.15a68.39,68.39,0,0,1-32.5-19c3.73-2.09,12.3-5.14,22.81-4.92,13.41.3,19.6,5.29,25.49,8.57,7.74,4.4,15.2,11,26.08,12.07"></path>
        <path class="ac0b5b28-f94c-4720-9e2e-71e4531b0b17" d="M384.33,80.68l-.22-.24c.05.07.14.12.21.2s0,0,0,0"></path>
        <polygon class="ac0b5b28-f94c-4720-9e2e-71e4531b0b17" points="429.94 46.94 430.11 47.07 430.11 46.94 429.94 46.94"></polygon>
        <path class="ac0b5b28-f94c-4720-9e2e-71e4531b0b17" d="M210.52,42.13,192.31,85.46l-17-43.33H157.2l25.12,56.59a16.91,16.91,0,0,0,15.46,10l30.29-66.64Z"></path>
        <path class="ac0b5b28-f94c-4720-9e2e-71e4531b0b17" d="M376.57,73.48q0-15.54,9-25.31t23.69-9.77q15.48,0,24.05,9.39t8.56,25.69q0,16.24-8.72,25.83t-23.89,9.57q-15.48,0-24.08-9.67t-8.59-25.73m16.5,0q0,22.47,16.17,22.46A13.94,13.94,0,0,0,421,90.11c2.88-3.9,4.34-9.44,4.34-16.63q0-22.14-16.12-22.14a14,14,0,0,0-11.8,5.84q-4.38,5.84-4.37,16.3"></path>
        <path class="ac0b5b28-f94c-4720-9e2e-71e4531b0b17" d="M369,45.16l-6.9,12.06q-5.67-5.36-15.21-5.35-9.15,0-14.47,6.09t-5.32,16.85q0,21.72,20.69,21.73A23.34,23.34,0,0,0,363.6,90.6l5.93,12.7a40.71,40.71,0,0,1-12.28,5.54,57.65,57.65,0,0,1-12.4,1.16q-16,0-25.24-9.31t-9.25-25.88q0-16.31,10.12-26.36T348.07,38.4A33.69,33.69,0,0,1,369,45.16"></path>
        <path class="ac0b5b28-f94c-4720-9e2e-71e4531b0b17" d="M297.69,107.7c-8.67.36-15.09-2.61-15.16-7.73q-9.33,8.72-20.06,8.72a28.9,28.9,0,0,1-22.41-9.79q-8.79-10-8.79-25,0-14.7,8.79-24.49a28.33,28.33,0,0,1,22-9.8q11.4,0,20.46,9.39V41.46h15.16Zm-51-33.76q0,9.39,5,15.3a16.5,16.5,0,0,0,13,6,17.34,17.34,0,0,0,13.55-5.77q5.18-6,5.17-15.17T278.3,59.11a17.07,17.07,0,0,0-13.42-5.84,16.68,16.68,0,0,0-13,5.91,21.78,21.78,0,0,0-5.17,14.76"></path>
      </g>
    </g>
  </svg>
  <h2>Password Reset Required</h2>
  <p class="reset-subtitle">Please change your password to continue</p>
  <form [formGroup]="passwordResetForm" (ngSubmit)="onSubmit()">
    <mat-form-field class="custom-field">
      <mat-label>New Password</mat-label>
      <input
        matInput
        [type]="hidePassword ? 'password' : 'text'"
        formControlName="newPassword"
        placeholder="Enter new password"
      >
      <button mat-icon-button matSuffix (click)="hidePassword = !hidePassword" type="button">
        <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
      </button>
      <mat-error *ngIf="newPassword?.invalid && newPassword?.touched">
        <span *ngIf="newPassword?.errors?.['required']">Password is required.</span>
        <span *ngIf="newPassword?.errors?.['minlength']">Password must be at least 8 characters long.</span>
        <span *ngIf="newPassword?.errors?.['pattern']">
          Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character (@$!%*?&).
        </span>
      </mat-error>
      <mat-hint *ngIf="!newPassword?.invalid || !newPassword?.touched">
        Must be at least 8 characters with uppercase, lowercase, number, and special character.
      </mat-hint>
    </mat-form-field>

    <mat-form-field class="custom-field">
      <mat-label>Confirm Password</mat-label>
      <input
        matInput
        [type]="hideConfirmPassword ? 'password' : 'text'"
        formControlName="confirmPassword"
        placeholder="Confirm your password"
      >
      <button mat-icon-button matSuffix (click)="hideConfirmPassword = !hideConfirmPassword" type="button">
        <mat-icon>{{hideConfirmPassword ? 'visibility_off' : 'visibility'}}</mat-icon>
      </button>
      <mat-error *ngIf="confirmPassword?.invalid && confirmPassword?.touched">
        <span *ngIf="confirmPassword?.errors?.['required']">Please confirm your password.</span>
      </mat-error>
      <mat-error *ngIf="hasPasswordMismatch">
        Passwords do not match.
      </mat-error>
    </mat-form-field>

    <div class="buttons">
      <button
        mat-raised-button
        color="primary"
        type="submit"
        class="action-button"
        [disabled]="passwordResetForm.invalid || isSubmitting"
      >
        <span *ngIf="!isSubmitting">Change Password</span>
        <span *ngIf="isSubmitting">Changing Password...</span>
      </button>
    </div>
  </form>

</div>