.breadcrumb-container {
  padding: 8px 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.breadcrumb {
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  margin: 0;
  list-style: none;
  align-items: center;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 14px;
}

.breadcrumb-item a {
  color: #134472;
  text-decoration: none;
  transition: color 0.2s;
}

.breadcrumb-item a:hover {
  color: #0056b3;
  text-decoration: underline;
}

.breadcrumb-item.active {
  color: #333;
  font-weight: 500;
}

.breadcrumb-item + .breadcrumb-item {
  padding-left: 8px;
}

.breadcrumb-item + .breadcrumb-item::before {
  display: inline-block;
  padding-right: 8px;
  color: #999;
  content: "/";
}

.home-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
  vertical-align: middle;
}
