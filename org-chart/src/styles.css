/* You can add global styles to this file, and also import other style files */

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

body {
  margin: 0;
  font-family: Robot<PERSON>, "Helvetica Neue", sans-serif;
  background-color: #f5f7fa;
  color: #333;
}

/* Global utility classes */
.container {
  padding: 20px;
}

/* Card styling */
.card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-title {
  font-size: 18px;
  font-weight: 500;
  color: #134472;
  margin: 0;
}

.card-subtitle {
  font-size: 14px;
  color: #666;
  margin: 4px 0 0 0;
}

/* Button styling */
.btn-primary {
  background-color: #134472;
  color: white;
}

.btn-secondary {
  background-color: #f0f0f0;
  color: #333;
}

/* Table styling */
.table-container {
  overflow-x: auto;
  margin-bottom: 20px;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #999;
}
