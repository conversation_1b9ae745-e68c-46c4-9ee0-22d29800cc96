#!/bin/bash

# Hardware Spoofing Script
# This script helps spoof various hardware identifiers

set -e

echo "🔧 Setting up Hardware Spoofing Tools..."

# Function to install MAC address spoofing tools
install_mac_spoofing() {
    echo "📦 Installing MAC address spoofing tools..."
    sudo apt update
    sudo apt install -y macchanger ethtool
    
    echo "✅ MAC spoofing tools installed!"
}

# Function to create MAC address randomizer
create_mac_randomizer() {
    cat > mac-randomizer.sh << 'EOF'
#!/bin/bash

# MAC Address Randomizer
# Randomizes MAC addresses for network interfaces

# Function to randomize MAC address
randomize_mac() {
    local interface=$1
    
    if [ -z "$interface" ]; then
        echo "❌ No interface specified"
        echo "Available interfaces:"
        ip link show | grep -E '^[0-9]+:' | cut -d: -f2 | tr -d ' '
        return 1
    fi
    
    echo "🔄 Randomizing MAC address for $interface..."
    
    # Bring interface down
    sudo ip link set dev "$interface" down
    
    # Generate random MAC address
    sudo macchanger -r "$interface"
    
    # Bring interface back up
    sudo ip link set dev "$interface" up
    
    echo "✅ MAC address randomized for $interface"
    macchanger -s "$interface"
}

# Function to restore original MAC
restore_mac() {
    local interface=$1
    
    if [ -z "$interface" ]; then
        echo "❌ No interface specified"
        return 1
    fi
    
    echo "🔄 Restoring original MAC address for $interface..."
    
    sudo ip link set dev "$interface" down
    sudo macchanger -p "$interface"
    sudo ip link set dev "$interface" up
    
    echo "✅ Original MAC address restored for $interface"
}

# Function to set specific MAC
set_mac() {
    local interface=$1
    local mac=$2
    
    if [ -z "$interface" ] || [ -z "$mac" ]; then
        echo "❌ Interface and MAC address required"
        echo "Usage: $0 set <interface> <mac>"
        return 1
    fi
    
    echo "🔄 Setting MAC address $mac for $interface..."
    
    sudo ip link set dev "$interface" down
    sudo macchanger -m "$mac" "$interface"
    sudo ip link set dev "$interface" up
    
    echo "✅ MAC address set for $interface"
}

case "$1" in
    "random")
        randomize_mac "$2"
        ;;
    "restore")
        restore_mac "$2"
        ;;
    "set")
        set_mac "$2" "$3"
        ;;
    "show")
        interface=${2:-$(ip route | grep default | awk '{print $5}' | head -1)}
        echo "Current MAC address for $interface:"
        macchanger -s "$interface"
        ;;
    "auto")
        # Auto-randomize all network interfaces
        echo "🔄 Auto-randomizing all network interfaces..."
        for interface in $(ip link show | grep -E '^[0-9]+:' | cut -d: -f2 | tr -d ' ' | grep -v lo); do
            randomize_mac "$interface"
        done
        ;;
    *)
        echo "Usage: $0 {random|restore|set|show|auto} [interface] [mac]"
        echo ""
        echo "Examples:"
        echo "  $0 random eth0           # Randomize MAC for eth0"
        echo "  $0 restore eth0          # Restore original MAC"
        echo "  $0 set eth0 aa:bb:cc:dd:ee:ff  # Set specific MAC"
        echo "  $0 show eth0             # Show current MAC"
        echo "  $0 auto                  # Randomize all interfaces"
        exit 1
        ;;
esac
EOF

    chmod +x mac-randomizer.sh
    echo "✅ MAC randomizer script created!"
}

# Function to install CPU/Hardware spoofing tools
install_hardware_spoofing() {
    echo "📦 Installing hardware spoofing tools..."
    sudo apt update
    sudo apt install -y dmidecode lshw
    
    # Install cpuid spoofing (if available)
    if command -v git >/dev/null 2>&1; then
        echo "📥 Downloading hardware spoofing tools..."
        git clone https://github.com/a0rtega/pafish.git /tmp/pafish 2>/dev/null || true
    fi
    
    echo "✅ Hardware spoofing tools installed!"
}

# Function to create hardware info spoofer
create_hardware_spoofer() {
    cat > hardware-spoofer.sh << 'EOF'
#!/bin/bash

# Hardware Information Spoofer
# Modifies various hardware identifiers

# Function to spoof CPU information
spoof_cpu_info() {
    echo "🔄 Spoofing CPU information..."
    
    # Create fake cpuinfo
    sudo cp /proc/cpuinfo /proc/cpuinfo.backup 2>/dev/null || true
    
    # Generate random CPU model
    local cpu_models=(
        "Intel(R) Core(TM) i7-10700K CPU @ 3.80GHz"
        "Intel(R) Core(TM) i5-9600K CPU @ 3.70GHz"
        "AMD Ryzen 7 3700X 8-Core Processor"
        "AMD Ryzen 5 3600 6-Core Processor"
        "Intel(R) Core(TM) i9-11900K CPU @ 3.50GHz"
    )
    
    local random_cpu=${cpu_models[$RANDOM % ${#cpu_models[@]}]}
    echo "🔧 Spoofing CPU as: $random_cpu"
    
    # Note: Actual /proc/cpuinfo modification requires kernel modules
    echo "⚠️  Full CPU spoofing requires kernel-level modifications"
}

# Function to spoof system information
spoof_system_info() {
    echo "🔄 Spoofing system information..."
    
    # Generate random system info
    local manufacturers=("Dell Inc." "HP" "Lenovo" "ASUS" "Acer" "MSI")
    local models=("OptiPlex 7090" "EliteBook 840" "ThinkPad X1" "ROG Strix" "Aspire 5" "Modern 14")
    
    local random_manufacturer=${manufacturers[$RANDOM % ${#manufacturers[@]}]}
    local random_model=${models[$RANDOM % ${#models[@]}]}
    
    echo "🔧 Spoofing system as: $random_manufacturer $random_model"
    
    # Create environment variables for spoofed info
    export SPOOFED_MANUFACTURER="$random_manufacturer"
    export SPOOFED_MODEL="$random_model"
    export SPOOFED_SERIAL="$(openssl rand -hex 8 | tr '[:lower:]' '[:upper:]')"
    
    echo "✅ System info spoofed (environment variables set)"
}

# Function to spoof disk information
spoof_disk_info() {
    echo "🔄 Spoofing disk information..."
    
    local disk_models=(
        "Samsung SSD 980 PRO 1TB"
        "WD Blue SN570 1TB"
        "Crucial MX570 1TB"
        "Kingston NV2 1TB"
        "Seagate BarraCuda 1TB"
    )
    
    local random_disk=${disk_models[$RANDOM % ${#disk_models[@]}]}
    echo "🔧 Spoofing disk as: $random_disk"
    
    export SPOOFED_DISK_MODEL="$random_disk"
    export SPOOFED_DISK_SERIAL="$(openssl rand -hex 10 | tr '[:lower:]' '[:upper:]')"
    
    echo "✅ Disk info spoofed (environment variables set)"
}

# Function to create hardware info override script
create_hw_override() {
    cat > hw-info-override.sh << 'HWEOF'
#!/bin/bash

# Hardware Information Override Script
# Use this script to run applications with spoofed hardware info

# Set spoofed environment variables
export SPOOFED_MANUFACTURER="Dell Inc."
export SPOOFED_MODEL="OptiPlex 7090"
export SPOOFED_SERIAL="$(openssl rand -hex 8 | tr '[:lower:]' '[:upper:]')"
export SPOOFED_DISK_MODEL="Samsung SSD 980 PRO 1TB"
export SPOOFED_DISK_SERIAL="$(openssl rand -hex 10 | tr '[:lower:]' '[:upper:]')"

# Override common hardware detection commands
alias dmidecode='echo "Spoofed hardware info - use environment variables"'
alias lshw='echo "Spoofed hardware info - use environment variables"'
alias lscpu='echo "Spoofed CPU info - use environment variables"'

# Run the provided command with spoofed environment
exec "$@"
HWEOF

    chmod +x hw-info-override.sh
    echo "✅ Hardware override script created!"
}

# Function to show current hardware info
show_hardware_info() {
    echo "🔍 Current Hardware Information:"
    echo ""
    echo "CPU Information:"
    lscpu | head -10
    echo ""
    echo "System Information:"
    sudo dmidecode -s system-manufacturer 2>/dev/null || echo "N/A"
    sudo dmidecode -s system-product-name 2>/dev/null || echo "N/A"
    echo ""
    echo "Disk Information:"
    lsblk -o NAME,MODEL,SERIAL | head -5
    echo ""
    echo "Network Interfaces:"
    ip link show | grep -E '^[0-9]+:' | cut -d: -f2 | tr -d ' '
}

case "$1" in
    "cpu")
        spoof_cpu_info
        ;;
    "system")
        spoof_system_info
        ;;
    "disk")
        spoof_disk_info
        ;;
    "all")
        spoof_cpu_info
        spoof_system_info
        spoof_disk_info
        create_hw_override
        ;;
    "show")
        show_hardware_info
        ;;
    "override")
        create_hw_override
        ;;
    *)
        echo "Usage: $0 {cpu|system|disk|all|show|override}"
        echo ""
        echo "Examples:"
        echo "  $0 cpu       # Spoof CPU information"
        echo "  $0 system    # Spoof system information"
        echo "  $0 disk      # Spoof disk information"
        echo "  $0 all       # Spoof all hardware info"
        echo "  $0 show      # Show current hardware info"
        echo "  $0 override  # Create hardware override script"
        exit 1
        ;;
esac
EOF

    chmod +x hardware-spoofer.sh
    echo "✅ Hardware spoofer script created!"
}

# Function to create browser fingerprint spoofing
create_browser_fingerprint_spoofer() {
    cat > browser-fingerprint-spoof.sh << 'EOF'
#!/bin/bash

# Browser Fingerprint Spoofing Script
# Launches browsers with spoofed fingerprints

# User agents for spoofing
USER_AGENTS=(
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0"
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0"
)

# Screen resolutions for spoofing
SCREEN_SIZES=(
    "1920,1080"
    "1366,768"
    "1440,900"
    "1536,864"
    "1280,720"
)

launch_spoofed_chrome() {
    local user_agent=${USER_AGENTS[$RANDOM % ${#USER_AGENTS[@]}]}
    local screen_size=${SCREEN_SIZES[$RANDOM % ${#SCREEN_SIZES[@]}]}
    local profile_dir="/tmp/chrome-spoofed-$(date +%s)"
    
    echo "🚀 Launching Chrome with spoofed fingerprint..."
    echo "User Agent: $user_agent"
    echo "Screen Size: $screen_size"
    
    google-chrome \
        --user-data-dir="$profile_dir" \
        --user-agent="$user_agent" \
        --window-size="$screen_size" \
        --disable-web-security \
        --disable-features=VizDisplayCompositor \
        --disable-background-timer-throttling \
        --disable-backgrounding-occluded-windows \
        --disable-renderer-backgrounding \
        --incognito &
}

launch_spoofed_firefox() {
    local user_agent=${USER_AGENTS[$RANDOM % ${#USER_AGENTS[@]}]}
    local profile_dir="/tmp/firefox-spoofed-$(date +%s)"
    
    mkdir -p "$profile_dir"
    
    echo "🚀 Launching Firefox with spoofed fingerprint..."
    echo "User Agent: $user_agent"
    
    # Create Firefox profile with spoofed settings
    cat > "$profile_dir/user.js" << EOL
user_pref("general.useragent.override", "$user_agent");
user_pref("privacy.resistFingerprinting", true);
user_pref("privacy.trackingprotection.enabled", true);
user_pref("privacy.donottrackheader.enabled", true);
user_pref("dom.webdriver.enabled", false);
user_pref("dom.webdriver.enabled", false);
user_pref("useragent.enabled", false);
EOL

    firefox --profile "$profile_dir" --private-window &
}

case "$1" in
    "chrome")
        launch_spoofed_chrome
        ;;
    "firefox")
        launch_spoofed_firefox
        ;;
    *)
        echo "Usage: $0 {chrome|firefox}"
        echo ""
        echo "Examples:"
        echo "  $0 chrome    # Launch Chrome with spoofed fingerprint"
        echo "  $0 firefox   # Launch Firefox with spoofed fingerprint"
        exit 1
        ;;
esac
EOF

    chmod +x browser-fingerprint-spoof.sh
    echo "✅ Browser fingerprint spoofer created!"
}

# Main installation
echo "🔧 Hardware Spoofing Setup Options:"
echo "1. Install MAC address spoofing"
echo "2. Install hardware info spoofing"
echo "3. Create browser fingerprint spoofing"
echo "4. Install all"
echo ""
read -p "Choose option (1-4): " choice

case $choice in
    1)
        install_mac_spoofing
        create_mac_randomizer
        ;;
    2)
        install_hardware_spoofing
        create_hardware_spoofer
        ;;
    3)
        create_browser_fingerprint_spoofer
        ;;
    4)
        install_mac_spoofing
        create_mac_randomizer
        install_hardware_spoofing
        create_hardware_spoofer
        create_browser_fingerprint_spoofer
        ;;
    *)
        echo "❌ Invalid option"
        exit 1
        ;;
esac

echo ""
echo "✅ Hardware spoofing setup complete!"
echo ""
echo "📋 Available tools:"
echo "- ./mac-randomizer.sh - MAC address spoofing"
echo "- ./hardware-spoofer.sh - Hardware info spoofing"
echo "- ./browser-fingerprint-spoof.sh - Browser fingerprint spoofing"
