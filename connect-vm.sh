#!/bin/bash

set -e  # Exit on any error

# === CONFIGURE these variables ===
PROJECT_ID="ops-excellence"
VM_NAME="teamsphere-staging"
ZONE="asia-south2-c"
REMOTE_PATH="/home/<USER>/"
REMOTE_PATH_PIYUSH="/home/<USER>/"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Function to check if gcloud is installed and authenticated
check_gcloud() {
    print_header "Checking Google Cloud CLI"
    
    if ! command -v gcloud &> /dev/null; then
        print_error "gcloud CLI is not installed. Please install it first:"
        echo "https://cloud.google.com/sdk/docs/install"
        exit 1
    fi
    
    print_status "gcloud CLI is installed"
    
    # Check if authenticated
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        print_warning "Not authenticated with gcloud. Running authentication..."
        gcloud auth login
    fi
    
    # Set project
    print_status "Setting project to: $PROJECT_ID"
    gcloud config set project "$PROJECT_ID"
    
    print_status "Current gcloud configuration:"
    gcloud config list
}

# Function to check VM status
check_vm_status() {
    print_header "Checking VM Status"
    
    local vm_status
    vm_status=$(gcloud compute instances describe "$VM_NAME" \
        --zone="$ZONE" \
        --format="value(status)" 2>/dev/null || echo "NOT_FOUND")
    
    case "$vm_status" in
        "RUNNING")
            print_status "VM '$VM_NAME' is running"
            return 0
            ;;
        "STOPPED"|"TERMINATED")
            print_warning "VM '$VM_NAME' is stopped. Starting it..."
            start_vm
            return 0
            ;;
        "NOT_FOUND")
            print_error "VM '$VM_NAME' not found in zone '$ZONE'"
            list_vms
            exit 1
            ;;
        *)
            print_warning "VM '$VM_NAME' is in state: $vm_status"
            print_status "Waiting for VM to be ready..."
            sleep 5
            check_vm_status
            ;;
    esac
}

# Function to start VM
start_vm() {
    print_status "Starting VM '$VM_NAME'..."
    gcloud compute instances start "$VM_NAME" --zone="$ZONE"
    
    print_status "Waiting for VM to be ready..."
    sleep 10
    
    # Wait for VM to be running
    while true; do
        local status
        status=$(gcloud compute instances describe "$VM_NAME" \
            --zone="$ZONE" \
            --format="value(status)")
        
        if [ "$status" = "RUNNING" ]; then
            print_status "VM is now running"
            break
        fi
        
        print_status "VM status: $status. Waiting..."
        sleep 5
    done
}

# Function to list available VMs
list_vms() {
    print_header "Available VMs in project '$PROJECT_ID'"
    gcloud compute instances list
}

# Function to get VM external IP
get_vm_ip() {
    local external_ip
    external_ip=$(gcloud compute instances describe "$VM_NAME" \
        --zone="$ZONE" \
        --format="value(networkInterfaces[0].accessConfigs[0].natIP)")
    
    if [ -n "$external_ip" ]; then
        print_status "VM External IP: $external_ip"
        echo "$external_ip"
    else
        print_warning "No external IP found for VM"
        return 1
    fi
}

# Function to connect via SSH
connect_ssh() {
    print_header "Connecting to VM via SSH"
    
    local username="${1:-$(whoami)}"
    
    print_status "Connecting as user: $username"
    print_status "VM: $VM_NAME"
    print_status "Zone: $ZONE"
    
    # Use gcloud compute ssh for automatic key management
    gcloud compute ssh "$username@$VM_NAME" \
        --zone="$ZONE" \
        --project="$PROJECT_ID"
}

# Function to connect via SSH with custom command
connect_ssh_command() {
    local command="$1"
    local username="${2:-$(whoami)}"
    
    print_header "Executing command on VM"
    print_status "Command: $command"
    
    gcloud compute ssh "$username@$VM_NAME" \
        --zone="$ZONE" \
        --project="$PROJECT_ID" \
        --command="$command"
}

# Function to copy files to VM
copy_to_vm() {
    local local_path="$1"
    local remote_path="$2"
    local username="${3:-$(whoami)}"
    
    print_header "Copying files to VM"
    print_status "From: $local_path"
    print_status "To: $username@$VM_NAME:$remote_path"
    
    gcloud compute scp "$local_path" "$username@$VM_NAME:$remote_path" \
        --zone="$ZONE" \
        --project="$PROJECT_ID" \
        --recurse
}

# Function to copy files from VM
copy_from_vm() {
    local remote_path="$1"
    local local_path="$2"
    local username="${3:-$(whoami)}"
    
    print_header "Copying files from VM"
    print_status "From: $username@$VM_NAME:$remote_path"
    print_status "To: $local_path"
    
    gcloud compute scp "$username@$VM_NAME:$remote_path" "$local_path" \
        --zone="$ZONE" \
        --project="$PROJECT_ID" \
        --recurse
}

# Function to show help
show_help() {
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  connect [username]           - Connect to VM via SSH (default: current user)"
    echo "  status                       - Check VM status"
    echo "  start                        - Start the VM"
    echo "  stop                         - Stop the VM"
    echo "  restart                      - Restart the VM"
    echo "  ip                          - Get VM external IP"
    echo "  list                        - List all VMs in project"
    echo "  exec 'command' [username]   - Execute command on VM"
    echo "  upload local_path remote_path [username] - Upload files to VM"
    echo "  download remote_path local_path [username] - Download files from VM"
    echo "  help                        - Show this help"
    echo ""
    echo "Examples:"
    echo "  $0 connect                  - Connect as current user"
    echo "  $0 connect vbs_tms          - Connect as vbs_tms user"
    echo "  $0 exec 'ls -la' vbs_tms    - Execute command as vbs_tms user"
    echo "  $0 upload ./app $REMOTE_PATH vbs_tms - Upload app folder"
}

# Main script logic
main() {
    local command="${1:-connect}"
    
    case "$command" in
        "connect")
            check_gcloud
            check_vm_status
            connect_ssh "$2"
            ;;
        "status")
            check_gcloud
            check_vm_status
            get_vm_ip
            ;;
        "start")
            check_gcloud
            start_vm
            ;;
        "stop")
            check_gcloud
            print_status "Stopping VM '$VM_NAME'..."
            gcloud compute instances stop "$VM_NAME" --zone="$ZONE"
            ;;
        "restart")
            check_gcloud
            print_status "Restarting VM '$VM_NAME'..."
            gcloud compute instances reset "$VM_NAME" --zone="$ZONE"
            ;;
        "ip")
            check_gcloud
            get_vm_ip
            ;;
        "list")
            check_gcloud
            list_vms
            ;;
        "exec")
            if [ -z "$2" ]; then
                print_error "Command required for exec"
                show_help
                exit 1
            fi
            check_gcloud
            check_vm_status
            connect_ssh_command "$2" "$3"
            ;;
        "upload")
            if [ -z "$2" ] || [ -z "$3" ]; then
                print_error "Local and remote paths required for upload"
                show_help
                exit 1
            fi
            check_gcloud
            check_vm_status
            copy_to_vm "$2" "$3" "$4"
            ;;
        "download")
            if [ -z "$2" ] || [ -z "$3" ]; then
                print_error "Remote and local paths required for download"
                show_help
                exit 1
            fi
            check_gcloud
            check_vm_status
            copy_from_vm "$2" "$3" "$4"
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "Unknown command: $command"
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
