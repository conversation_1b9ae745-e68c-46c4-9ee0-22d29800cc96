version: '3.8'

services:
  # PostgreSQL Database - Production Configuration
  postgres:
    image: postgres:15-alpine
    container_name: teamsphere-postgres-prod
    environment:
      POSTGRES_DB: vbs_allocation_caps
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-voyage}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
      - ./docker/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - ./backups:/backups
    networks:
      - teamsphere-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d vbs_allocation_caps"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: always
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # TeamSphere Application - Production Configuration
  teamsphere-app:
    image: ${DOCKER_IMAGE:-teamsphere:latest}
    container_name: teamsphere-app-prod
    environment:
      - SPRING_PROFILES_ACTIVE=docker,prod
      - SPRING_DATASOURCE_URL=***************************************************
      - SPRING_DATASOURCE_USERNAME=postgres
      - SPRING_DATASOURCE_PASSWORD=${POSTGRES_PASSWORD:-voyage}
      - SERVER_PORT=8080
      - JAVA_OPTS=-Xmx1g -Xms512m
      - AWS_SES_ACCESS_KEY=${AWS_SES_ACCESS_KEY}
      - AWS_SES_SECRET_KEY=${AWS_SES_SECRET_KEY}
      - AWS_SES_REGION=${AWS_SES_REGION:-us-east-1}
      - AWS_SES_FROM_EMAIL=${AWS_SES_FROM_EMAIL:-<EMAIL>}
    ports:
      - "80:80"
      - "8080:8080"
    volumes:
      - app_uploads_prod:/app/uploads
      - app_logs_prod:/app/logs
      - app_backups_prod:/app/database_backups
      - ./docker/credentials:/app/credentials:ro
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - teamsphere-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
    restart: always
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'

  # Nginx Load Balancer (if scaling)
  nginx-lb:
    image: nginx:alpine
    container_name: teamsphere-nginx-lb
    volumes:
      - ./docker/nginx-lb.conf:/etc/nginx/nginx.conf:ro
    ports:
      - "443:443"
    depends_on:
      - teamsphere-app
    networks:
      - teamsphere-network
    restart: always
    profiles:
      - lb

volumes:
  postgres_data_prod:
    driver: local
  app_uploads_prod:
    driver: local
  app_logs_prod:
    driver: local
  app_backups_prod:
    driver: local

networks:
  teamsphere-network:
    driver: bridge
