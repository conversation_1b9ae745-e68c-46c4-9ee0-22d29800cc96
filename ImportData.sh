#!/bin/bash

# Database credentials
DB_NAME="vbs_allocation_caps"
DB_USER="postgres"
DB_PASSWORD="voyage"
SQL_FILE="/home/<USER>/Downloads/vbs_allocation_caps_20250720_123748.sql"

# Export password so psql won't prompt for it
export PGPASSWORD=$DB_PASSWORD

echo "🛑 Terminating active connections to $DB_NAME..."
psql -h localhost -U $DB_USER -d postgres -c "
SELECT pg_terminate_backend(pid)
FROM pg_stat_activity
WHERE datname = '$DB_NAME' AND pid <> pg_backend_pid();
"

echo "🗑️ Dropping existing database..."
psql -h localhost -U $DB_USER -d postgres -c "DROP DATABASE IF EXISTS $DB_NAME;"

echo "🧱 Creating fresh database..."
psql -h localhost -U $DB_USER -d postgres -c "CREATE DATABASE $DB_NAME;"

echo "📦 Importing SQL data from: $SQL_FILE"
psql -h localhost -U $DB_USER -d $DB_NAME -f "$SQL_FILE"

echo "✅ Database reset and import completed."

