version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: teamsphere-postgres
    environment:
      POSTGRES_DB: vbs_allocation_caps
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: voyage
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5433:5432"
    networks:
      - teamsphere-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d vbs_allocation_caps"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # TeamSphere Application (Frontend + Backend)
  teamsphere-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: teamsphere-app
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_DATASOURCE_URL=***************************************************
      - SPRING_DATASOURCE_USERNAME=postgres
      - SPRING_DATASOURCE_PASSWORD=voyage
      - SERVER_PORT=8080
    ports:
      - "80:80"
      - "8080:8080"
    volumes:
      - app_uploads:/app/uploads
      - app_logs:/app/logs
      - app_backups:/app/database_backups
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - teamsphere-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped

  # Adminer for database management (optional, for development)
  adminer:
    image: adminer:4.8.1
    container_name: teamsphere-adminer
    ports:
      - "8081:8080"
    networks:
      - teamsphere-network
    restart: unless-stopped
    profiles:
      - dev

volumes:
  postgres_data:
    driver: local
  app_uploads:
    driver: local
  app_logs:
    driver: local
  app_backups:
    driver: local

networks:
  teamsphere-network:
    driver: bridge
