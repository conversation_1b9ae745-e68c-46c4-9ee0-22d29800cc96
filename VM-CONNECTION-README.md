# TeamSphere VM Connection Scripts

This directory contains scripts to easily connect to and manage your Google Cloud Compute Engine VM for the TeamSphere project.

## Prerequisites

1. **Google Cloud CLI**: Install and authenticate with gcloud
   ```bash
   # Install gcloud CLI (if not already installed)
   curl https://sdk.cloud.google.com | bash
   exec -l $SHELL
   
   # Authenticate
   gcloud auth login
   
   # Set project
   gcloud config set project ops-excellence
   ```

2. **VM Configuration**: Ensure your VM is properly configured with the following details:
   - Project ID: `ops-excellence`
   - VM Name: `teamsphere-staging`
   - Zone: `asia-south2-c`

## Scripts Overview

### 1. `connect-vm.sh` - Main Connection Script

The primary script for VM operations with comprehensive functionality.

#### Usage:
```bash
./connect-vm.sh [COMMAND] [OPTIONS]
```

#### Commands:
- `connect [username]` - Connect to VM via SSH
- `status` - Check VM status and get IP
- `start` - Start the VM if stopped
- `stop` - Stop the VM
- `restart` - Restart the VM
- `ip` - Get VM external IP address
- `list` - List all VMs in the project
- `exec 'command' [username]` - Execute command on VM
- `upload local_path remote_path [username]` - Upload files to VM
- `download remote_path local_path [username]` - Download files from VM

#### Examples:
```bash
# Connect as current user
./connect-vm.sh connect

# Connect as vbs_tms user
./connect-vm.sh connect vbs_tms

# Check VM status
./connect-vm.sh status

# Execute command on VM
./connect-vm.sh exec 'ls -la /home/<USER>' vbs_tms

# Upload project files
./connect-vm.sh upload /home/<USER>/Documents/teamsphere /home/<USER>/ vbs_tms

# Download logs
./connect-vm.sh download /home/<USER>/app.log ./logs/ vbs_tms
```

### 2. `quick-connect.sh` - Interactive Menu

An interactive script with a menu for common operations.

#### Usage:
```bash
./quick-connect.sh
```

This will show an interactive menu with options for:
1. Connect as current user
2. Connect as vbs_tms user
3. Connect as piyush_mishra user
4. Check VM status
5. Start VM
6. Get VM IP
7. Upload project files to vbs_tms
8. Upload project files to piyush_mishra
9. Execute custom command

### 3. `deploy-to-vm.sh` - Deployment Script

Specialized script for deploying the TeamSphere application.

#### Usage:
```bash
./deploy-to-vm.sh [COMMAND] [OPTIONS]
```

#### Commands:
- `backend [username] [remote_path]` - Deploy backend only
- `frontend [username] [remote_path]` - Deploy frontend only
- `full [username] [remote_path]` - Deploy full project
- `status [username]` - Check service status
- `logs [username] [service] [remote_path]` - View logs

#### Examples:
```bash
# Deploy full project to vbs_tms user
./deploy-to-vm.sh full vbs_tms

# Deploy only backend
./deploy-to-vm.sh backend vbs_tms

# Check service status
./deploy-to-vm.sh status vbs_tms

# View backend logs
./deploy-to-vm.sh logs vbs_tms backend
```

## Common Workflows

### First Time Setup
1. Ensure VM is running: `./connect-vm.sh start`
2. Connect to VM: `./connect-vm.sh connect vbs_tms`
3. Install dependencies on VM (Java, Node.js, etc.)

### Development Deployment
1. Deploy full project: `./deploy-to-vm.sh full vbs_tms`
2. Check services: `./deploy-to-vm.sh status vbs_tms`
3. View logs if needed: `./deploy-to-vm.sh logs vbs_tms backend`

### Quick Connection
1. Run interactive menu: `./quick-connect.sh`
2. Choose appropriate option from menu

### File Transfer
```bash
# Upload local changes
./connect-vm.sh upload ./backend /home/<USER>/ vbs_tms

# Download logs or configs
./connect-vm.sh download /home/<USER>/logs ./local-logs/ vbs_tms
```

## VM User Accounts

The scripts support connecting to different user accounts on the VM:

- **vbs_tms**: Primary application user (default)
  - Home: `/home/<USER>/`
  - Use for: Application deployment and management

- **piyush_mishra**: Secondary user account
  - Home: `/home/<USER>/`
  - Use for: Development and testing

- **Current user**: Your local username
  - Use for: General administration

## Troubleshooting

### VM Not Found
```bash
# List all VMs to verify name and zone
./connect-vm.sh list
```

### Authentication Issues
```bash
# Re-authenticate with gcloud
gcloud auth login
gcloud config set project ops-excellence
```

### SSH Key Issues
```bash
# Generate new SSH keys if needed
gcloud compute config-ssh
```

### VM Won't Start
```bash
# Check VM status and logs
./connect-vm.sh status
gcloud compute instances get-serial-port-output teamsphere-staging --zone=asia-south2-c
```

### Service Issues
```bash
# Check what's running on the VM
./deploy-to-vm.sh status vbs_tms

# View application logs
./deploy-to-vm.sh logs vbs_tms backend
```

## Security Notes

1. **SSH Keys**: The scripts use gcloud's automatic SSH key management
2. **Firewall**: Ensure VM has appropriate firewall rules for your application ports
3. **User Permissions**: Make sure the target user has appropriate permissions for deployment paths

## Configuration

To modify the default configuration, edit the variables at the top of each script:

```bash
PROJECT_ID="ops-excellence"
VM_NAME="teamsphere-staging"
ZONE="asia-south2-c"
LOCAL_BASE_PATH="/home/<USER>/Documents/teamsphere"
REMOTE_PATH_VBS="/home/<USER>/"
REMOTE_PATH_PIYUSH="/home/<USER>/"
```

## Support

If you encounter issues:
1. Check VM status: `./connect-vm.sh status`
2. Verify gcloud authentication: `gcloud auth list`
3. Check project configuration: `gcloud config list`
4. Review VM logs: `gcloud compute instances get-serial-port-output teamsphere-staging --zone=asia-south2-c`
