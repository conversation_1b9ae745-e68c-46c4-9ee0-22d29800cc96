# Git
.git
.gitignore
.gitattributes

# Documentation
*.md
docs/
*.txt

# IDE
.vscode/
.idea/
*.iml
*.iws
*.ipr

# OS
.DS_Store
Thumbs.db

# Node.js
org-chart/node_modules/
org-chart/dist/
org-chart/.angular/
org-chart/coverage/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Maven
backend/capsAllocation/target/
backend/capsAllocation/.mvn/wrapper/maven-wrapper.jar
backend/out/

# Spring Boot
backend/capsAllocation/logs/
backend/capsAllocation/database_backups/
backend/capsAllocation/tokens/

# Test automation
teamsphere-test-automation/

# Deployment scripts (keep only docker related)
deploy.sh
install_dependecies.sh
postgres_check_install.sh
connect-debug.sh

# Database dumps
*.dump
*.sql

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
.cache/

# Environment files (will be handled separately)
.env
.env.local
.env.production

# Backup files
*~
*.bak
*.swp
*.swo

# Credentials (will be mounted as volumes)
backend/capsAllocation/src/main/resources/ops-excellence-*.json
backend/capsAllocation/src/main/resources/credentials.json
