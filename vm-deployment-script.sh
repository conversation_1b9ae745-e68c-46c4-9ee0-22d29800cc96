#!/bin/bash

# TeamSphere VM Deployment Script
# This script runs on the VM to deploy and test the installer

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

REMOTE_PATH="/home/<USER>/"

echo '🚀 Starting complete TeamSphere deployment...'
echo '=============================================='

# Stop existing services if they exist
log_info "Stopping existing services..."
sudo systemctl stop teamsphere 2>/dev/null || echo 'teamsphere service not running'
sudo systemctl stop nginx 2>/dev/null || echo 'nginx not running'

# Kill any Java processes that might be running
sudo pkill -f 'java.*teamsphere' 2>/dev/null || echo 'No Java processes found'
sudo pkill -f 'java.*capsAllocation' 2>/dev/null || echo 'No capsAllocation processes found'

# Remove existing systemd service if it exists
sudo systemctl disable teamsphere 2>/dev/null || echo 'teamsphere service not enabled'
sudo rm -f /etc/systemd/system/teamsphere.service
sudo systemctl daemon-reload

log_success "Existing services stopped"

# Change to installer directory
cd ${REMOTE_PATH}installer

log_info "Starting TeamSphere Compute Engine installation..."
echo '=================================================='

# Run the installer
sudo ./teamsphere-compute-engine-installer.bin

log_success "Installation completed"

# Wait a moment for services to start
log_info "Waiting for services to start..."
sleep 15

log_info "Testing deployment..."

# Check if services are running
echo 'Checking service status:'
sudo systemctl status teamsphere --no-pager || echo 'TeamSphere service status check failed'
sudo systemctl status nginx --no-pager || echo 'Nginx service status check failed'
sudo systemctl status postgresql --no-pager || echo 'PostgreSQL service status check failed'

# Check if ports are listening
echo 'Checking listening ports:'
sudo netstat -tlnp | grep ':80 ' || echo 'Port 80 not listening'
sudo netstat -tlnp | grep ':8080 ' || echo 'Port 8080 not listening'

# Test frontend accessibility
echo 'Testing frontend accessibility:'
FRONTEND_STATUS=$(curl -s -o /dev/null -w '%{http_code}' http://localhost/ || echo 'Frontend test failed')
echo "Frontend HTTP Status: $FRONTEND_STATUS"

# Test backend API accessibility
echo 'Testing backend API accessibility:'
BACKEND_STATUS=$(curl -s -o /dev/null -w '%{http_code}' http://localhost:8080/actuator/health || echo 'Backend health check failed')
echo "Backend API HTTP Status: $BACKEND_STATUS"

# Wait for application to be fully ready
log_info "Waiting for application to be ready..."
for i in {1..30}; do
  if curl -s http://localhost:8080/actuator/health >/dev/null 2>&1; then
    log_success "Application is ready!"
    break
  fi
  echo "Attempt $i/30: Waiting for application..."
  sleep 5
done

# Test login endpoint
log_info "Testing login functionality..."
LOGIN_RESPONSE=$(curl -s -X POST 'http://localhost:8080/auth/login' \
  -H 'Content-Type: application/json' \
  -d '{"username":"admin","password":"admin"}' \
  -w '%{http_code}' -o /tmp/login_response.txt)

echo "Login HTTP Status: $LOGIN_RESPONSE"
echo 'Login Response:'
cat /tmp/login_response.txt || echo 'No login response'

# Test frontend loading
echo 'Testing frontend loading...'
FRONTEND_RESPONSE=$(curl -s -w '%{http_code}' -o /tmp/frontend_response.txt http://localhost/)
echo "Frontend HTTP Status: $FRONTEND_RESPONSE"

if [ "$FRONTEND_RESPONSE" = "200" ]; then
  log_success "Frontend is accessible"
else
  log_error "Frontend is not accessible"
fi

# Check if login was successful (look for token or success indicator)
if grep -q -i 'token\|success\|jwt' /tmp/login_response.txt 2>/dev/null; then
  log_success "Login appears to be successful!"
else
  log_warning "Login may have failed"
  echo 'Login response content:'
  cat /tmp/login_response.txt 2>/dev/null || echo 'No response content'
fi

# Check application logs
echo 'Recent application logs:'
sudo journalctl -u teamsphere -n 20 --no-pager || echo 'No application logs found'

# Get external IP for testing
EXTERNAL_IP=$(curl -s http://metadata.google.internal/computeMetadata/v1/instance/network-interfaces/0/access-configs/0/external-ip -H "Metadata-Flavor: Google" 2>/dev/null || echo "Unable to get external IP")

log_success "Deployment and testing completed!"
echo ""
echo "=== DEPLOYMENT SUMMARY ==="
echo "Frontend Status: $FRONTEND_RESPONSE"
echo "Backend Status: $BACKEND_STATUS"
echo "Login Status: $LOGIN_RESPONSE"
echo ""
echo "=== ACCESS INFORMATION ==="
echo "External IP: $EXTERNAL_IP"
echo "Frontend URL: http://$EXTERNAL_IP"
echo "Backend API URL: http://$EXTERNAL_IP:8080"
echo "Login credentials: admin/admin"
echo ""
echo "If domains are configured:"
echo "Frontend: http://teamsphere.in"
echo "Backend API: http://api.teamsphere.in"
echo ""

# Clean up test files
rm -f /tmp/login_response.txt /tmp/frontend_response.txt

log_success "🎉 TeamSphere deployment and testing completed!"

# Final verification
if [ "$FRONTEND_RESPONSE" = "200" ] && [ "$LOGIN_RESPONSE" = "200" ]; then
  log_success "✅ ALL TESTS PASSED - TeamSphere is ready for use!"
  exit 0
else
  log_warning "⚠️  Some tests failed - please check the logs above"
  exit 1
fi
