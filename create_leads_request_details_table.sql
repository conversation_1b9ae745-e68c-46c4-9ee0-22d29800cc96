-- Manual script to create leads_request_details table
-- Run this script manually in PostgreSQL if the table doesn't exist or has wrong schema

-- Drop table if it exists (be careful in production!)
-- DROP TABLE IF EXISTS leads_request_details;

-- Create the table with correct schema
CREATE TABLE IF NOT EXISTS leads_request_details (
    id BIGSERIAL PRIMARY KEY,
    leads_request_id BIGINT NOT NULL,
    target_ldap VARCHAR(255) NOT NULL,
    field_name VARCHAR(255) NOT NULL,
    old_value TEXT,
    new_value TEXT,
    change_type VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata TEXT,
    
    -- Foreign key constraint
    CONSTRAINT fk_leads_request_details_request_id 
        FOREIGN KEY (leads_request_id) 
        REFERENCES leads_request(id) 
        ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_leads_request_details_request_id ON leads_request_details(leads_request_id);
CREATE INDEX IF NOT EXISTS idx_leads_request_details_target_ldap ON leads_request_details(target_ldap);
CREATE INDEX IF NOT EXISTS idx_leads_request_details_change_type ON leads_request_details(change_type);
CREATE INDEX IF NOT EXISTS idx_leads_request_details_created_at ON leads_request_details(created_at);

-- Add comments
COMMENT ON TABLE leads_request_details IS 'Stores individual field changes for leads requests, replacing LOB usage';
COMMENT ON COLUMN leads_request_details.leads_request_id IS 'Foreign key to leads_request table';
COMMENT ON COLUMN leads_request_details.target_ldap IS 'LDAP of the employee being modified/added/deleted';
COMMENT ON COLUMN leads_request_details.field_name IS 'Name of the field being changed (e.g., firstName, team, etc.)';
COMMENT ON COLUMN leads_request_details.old_value IS 'Previous value (null for new employees)';
COMMENT ON COLUMN leads_request_details.new_value IS 'New value (null for deleted employees)';
COMMENT ON COLUMN leads_request_details.change_type IS 'Type of change: ADD, EDIT, DELETE';
COMMENT ON COLUMN leads_request_details.created_at IS 'When this detail was created';
COMMENT ON COLUMN leads_request_details.metadata IS 'JSON string for additional context if needed';

-- Verify the table was created
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'leads_request_details' 
ORDER BY ordinal_position;
