#!/bin/bash

set -e  # Exit on any error

# === CONFIGURE these variables (from gcs_upload_and_fetch.sh) ===
PROJECT_ID="ops-excellence"
BUCKET_NAME="teamsphere-installer-$(date +%s)"
LOCAL_BASE_PATH="/home/<USER>/Documents/teamsphere"
VM_NAME="teamsphere-staging"
ZONE="asia-south2-c"
REMOTE_PATH="/home/<USER>/"
CLEANUP=true

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# === Function to check for gcloud and install if missing ===
install_gcloud() {
  if ! command -v gcloud &> /dev/null; then
    log_info "gcloud not found. Installing Google Cloud SDK..."
    curl https://sdk.cloud.google.com | bash
    exec -l $SHELL
  else
    log_success "gcloud found."
  fi
}

# === Authenticate and configure gcloud ===
auth_gcloud() {
  log_info "Authenticating and setting project..."
  gcloud auth login --quiet
  gcloud config set project "$PROJECT_ID"
}

# === Check VM connectivity ===
check_vm_connectivity() {
  log_info "Checking VM connectivity..."
  
  # Check if VM is running
  VM_STATUS=$(gcloud compute instances describe "$VM_NAME" --zone="$ZONE" --format="get(status)" 2>/dev/null || echo "NOT_FOUND")
  
  if [ "$VM_STATUS" != "RUNNING" ]; then
    log_error "VM $VM_NAME is not running (status: $VM_STATUS)"
    log_info "Starting VM..."
    gcloud compute instances start "$VM_NAME" --zone="$ZONE"
    
    # Wait for VM to be ready
    log_info "Waiting for VM to be ready..."
    sleep 30
  fi
  
  log_success "VM is running"
}

# === Create bucket ===
create_bucket() {
  BUCKET_REGION="asia-south2"
  log_info "Creating bucket: $BUCKET_NAME in region $BUCKET_REGION"
  gsutil mb -l "$BUCKET_REGION" "gs://$BUCKET_NAME/"
}

# === Upload files to bucket ===
upload_files() {
  log_info "Uploading files to gs://$BUCKET_NAME/"

  cd "$LOCAL_BASE_PATH"

  # Check if installer directory exists
  if [ ! -d "build/teamsphere-compute-engine-installer-1.0.0" ]; then
    log_error "Installer directory not found at build/teamsphere-compute-engine-installer-1.0.0"
    log_info "Please run the installer creation script first"
    exit 1
  fi

  # Upload the entire installer directory as tar.gz
  log_info "Creating and uploading installer package..."
  cd build
  tar -czf teamsphere-compute-engine-installer.tar.gz teamsphere-compute-engine-installer-1.0.0/
  gsutil cp "teamsphere-compute-engine-installer.tar.gz" "gs://$BUCKET_NAME/"
  cd ..

  # Upload deployment guide
  if [ -f "COMPUTE_ENGINE_DEPLOYMENT_GUIDE.md" ]; then
    log_info "Uploading deployment guide..."
    gsutil cp "COMPUTE_ENGINE_DEPLOYMENT_GUIDE.md" "gs://$BUCKET_NAME/"
  fi

  # Upload VM deployment script
  if [ -f "vm-deployment-script.sh" ]; then
    log_info "Uploading VM deployment script..."
    gsutil cp "vm-deployment-script.sh" "gs://$BUCKET_NAME/"
  fi

  log_success "Files uploaded successfully"
}

# === Deploy and run on VM ===
deploy_and_run() {
  log_info "Deploying and running on VM..."
  
  # Try SSH connection with retries
  for attempt in {1..3}; do
    log_info "SSH attempt $attempt/3..."
    
    if gcloud compute ssh "$VM_NAME" --zone "$ZONE" --command "echo 'SSH connection successful'" 2>/dev/null; then
      log_success "SSH connection established"
      break
    else
      log_warning "SSH attempt $attempt failed"
      if [ $attempt -eq 3 ]; then
        log_error "Failed to establish SSH connection after 3 attempts"
        exit 1
      fi
      sleep 10
    fi
  done
  
  # Download files and run deployment
  gcloud compute ssh "$VM_NAME" --zone "$ZONE" --command "
    # Create deployment directory with proper permissions
    sudo mkdir -p ${REMOTE_PATH}installer
    sudo chown -R \$(whoami):\$(whoami) ${REMOTE_PATH}installer

    # Download files to home directory first
    echo '📥 Downloading installer package...'
    cd ~
    gsutil cp gs://$BUCKET_NAME/teamsphere-compute-engine-installer.tar.gz ./
    gsutil cp gs://$BUCKET_NAME/vm-deployment-script.sh ./ 2>/dev/null || echo 'No VM script found'
    gsutil cp gs://$BUCKET_NAME/COMPUTE_ENGINE_DEPLOYMENT_GUIDE.md ./ 2>/dev/null || echo 'No deployment guide found'

    # Extract installer package
    echo '📦 Extracting installer package...'
    tar -xzf teamsphere-compute-engine-installer.tar.gz

    # Move to installer directory
    sudo mv teamsphere-compute-engine-installer-1.0.0 ${REMOTE_PATH}installer/
    sudo mv vm-deployment-script.sh ${REMOTE_PATH}installer/ 2>/dev/null || echo 'No VM script to move'
    sudo mv COMPUTE_ENGINE_DEPLOYMENT_GUIDE.md ${REMOTE_PATH}installer/ 2>/dev/null || echo 'No guide to move'

    # Set proper permissions
    sudo chown -R \$(whoami):\$(whoami) ${REMOTE_PATH}installer
    chmod +x ${REMOTE_PATH}installer/teamsphere-compute-engine-installer-1.0.0/teamsphere-compute-engine-installer.sh

    echo '✅ Files downloaded and extracted'
    ls -la ${REMOTE_PATH}installer/
    ls -la ${REMOTE_PATH}installer/teamsphere-compute-engine-installer-1.0.0/

    echo '🚀 Running TeamSphere Compute Engine installer...'
    cd ${REMOTE_PATH}installer/teamsphere-compute-engine-installer-1.0.0

    # Run the installer
    ./teamsphere-compute-engine-installer.sh
  "
}

# === Get VM external IP for browser testing ===
get_vm_info() {
  log_info "Getting VM information for browser testing..."
  
  EXTERNAL_IP=$(gcloud compute instances describe "$VM_NAME" --zone="$ZONE" --format="get(networkInterfaces[0].accessConfigs[0].natIP)")
  
  log_success "VM External IP: $EXTERNAL_IP"
  log_info "You can test the application at:"
  log_info "  Frontend: http://$EXTERNAL_IP"
  log_info "  Backend API: http://$EXTERNAL_IP:8080"
  log_info "  Login credentials: admin/admin"
  
  # If domains are configured, show domain URLs too
  log_info "If domains are configured:"
  log_info "  Frontend: http://teamsphere.in"
  log_info "  Backend API: http://api.teamsphere.in"
}

# === Clean up bucket ===
cleanup_bucket() {
  if [ "$CLEANUP" = true ]; then
    log_info "Cleaning up GCS bucket..."
    gsutil -m rm -r "gs://$BUCKET_NAME/" 2>/dev/null || log_warning "Could not remove bucket"
    log_success "Bucket cleanup completed"
  fi
}

# === MAIN EXECUTION ===

log_info "Starting TeamSphere Compute Engine deployment and testing..."

install_gcloud
auth_gcloud
check_vm_connectivity
create_bucket
upload_files
deploy_and_run
get_vm_info
cleanup_bucket

log_success "TeamSphere Compute Engine deployment and testing completed!"
log_info "Check the test results above to verify login functionality."
