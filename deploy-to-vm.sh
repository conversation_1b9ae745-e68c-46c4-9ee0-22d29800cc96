#!/bin/bash

set -e  # Exit on any error

# === CONFIGURE these variables ===
PROJECT_ID="ops-excellence"
VM_NAME="teamsphere-staging"
ZONE="asia-south2-c"
LOCAL_BASE_PATH="/home/<USER>/Documents/teamsphere"
REMOTE_PATH_VBS="/home/<USER>/"
REMOTE_PATH_PIYUSH="/home/<USER>/"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Function to deploy backend
deploy_backend() {
    local username="${1:-vbs_tms}"
    local remote_path="${2:-$REMOTE_PATH_VBS}"
    
    print_header "Deploying Backend to VM"
    
    # Build backend locally first
    print_status "Building backend locally..."
    cd "$LOCAL_BASE_PATH/backend/capsAllocation"
    
    if [ -f "mvnw" ]; then
        ./mvnw clean package -DskipTests
    elif command -v mvn &> /dev/null; then
        mvn clean package -DskipTests
    else
        print_error "Maven not found. Please install Maven or use Maven wrapper."
        exit 1
    fi
    
    # Upload backend
    print_status "Uploading backend to VM..."
    gcloud compute scp --recurse "$LOCAL_BASE_PATH/backend" "$username@$VM_NAME:$remote_path" \
        --zone="$ZONE" \
        --project="$PROJECT_ID"
    
    # Start/restart backend service on VM
    print_status "Starting backend service on VM..."
    gcloud compute ssh "$username@$VM_NAME" \
        --zone="$ZONE" \
        --project="$PROJECT_ID" \
        --command="cd ${remote_path}backend/capsAllocation && nohup java -jar target/*.jar > app.log 2>&1 & echo \$! > app.pid"
    
    print_status "Backend deployment completed!"
}

# Function to deploy frontend
deploy_frontend() {
    local username="${1:-vbs_tms}"
    local remote_path="${2:-$REMOTE_PATH_VBS}"
    
    print_header "Deploying Frontend to VM"
    
    # Build frontend locally first
    print_status "Building frontend locally..."
    cd "$LOCAL_BASE_PATH/frontend"
    
    if [ -f "package.json" ]; then
        if command -v npm &> /dev/null; then
            npm install
            npm run build
        elif command -v yarn &> /dev/null; then
            yarn install
            yarn build
        else
            print_error "Node.js package manager not found. Please install npm or yarn."
            exit 1
        fi
    else
        print_error "package.json not found in frontend directory."
        exit 1
    fi
    
    # Upload frontend
    print_status "Uploading frontend to VM..."
    gcloud compute scp --recurse "$LOCAL_BASE_PATH/frontend" "$username@$VM_NAME:$remote_path" \
        --zone="$ZONE" \
        --project="$PROJECT_ID"
    
    print_status "Frontend deployment completed!"
}

# Function to deploy full project
deploy_full() {
    local username="${1:-vbs_tms}"
    local remote_path="${2:-$REMOTE_PATH_VBS}"
    
    print_header "Deploying Full Project to VM"
    
    # Stop existing services
    print_status "Stopping existing services..."
    gcloud compute ssh "$username@$VM_NAME" \
        --zone="$ZONE" \
        --project="$PROJECT_ID" \
        --command="pkill -f 'java -jar' || true"
    
    # Upload entire project
    print_status "Uploading project files..."
    gcloud compute scp --recurse "$LOCAL_BASE_PATH" "$username@$VM_NAME:$remote_path" \
        --zone="$ZONE" \
        --project="$PROJECT_ID"
    
    # Build and start backend
    print_status "Building and starting backend..."
    gcloud compute ssh "$username@$VM_NAME" \
        --zone="$ZONE" \
        --project="$PROJECT_ID" \
        --command="cd ${remote_path}teamsphere/backend/capsAllocation && ./mvnw clean package -DskipTests && nohup java -jar target/*.jar > app.log 2>&1 & echo \$! > app.pid"
    
    # Build frontend
    print_status "Building frontend..."
    gcloud compute ssh "$username@$VM_NAME" \
        --zone="$ZONE" \
        --project="$PROJECT_ID" \
        --command="cd ${remote_path}teamsphere/frontend && npm install && npm run build"
    
    print_status "Full deployment completed!"
}

# Function to check service status
check_services() {
    local username="${1:-vbs_tms}"
    
    print_header "Checking Service Status"
    
    gcloud compute ssh "$username@$VM_NAME" \
        --zone="$ZONE" \
        --project="$PROJECT_ID" \
        --command="echo 'Java processes:' && ps aux | grep java | grep -v grep || echo 'No Java processes found'"
    
    gcloud compute ssh "$username@$VM_NAME" \
        --zone="$ZONE" \
        --project="$PROJECT_ID" \
        --command="echo 'Node processes:' && ps aux | grep node | grep -v grep || echo 'No Node processes found'"
    
    gcloud compute ssh "$username@$VM_NAME" \
        --zone="$ZONE" \
        --project="$PROJECT_ID" \
        --command="echo 'Port usage:' && netstat -tlnp 2>/dev/null | grep -E ':(8080|3000|80|443)' || echo 'No services on common ports'"
}

# Function to view logs
view_logs() {
    local username="${1:-vbs_tms}"
    local service="${2:-backend}"
    local remote_path="${3:-$REMOTE_PATH_VBS}"
    
    print_header "Viewing $service Logs"
    
    case "$service" in
        "backend")
            gcloud compute ssh "$username@$VM_NAME" \
                --zone="$ZONE" \
                --project="$PROJECT_ID" \
                --command="cd ${remote_path}teamsphere/backend/capsAllocation && tail -f app.log"
            ;;
        "frontend")
            gcloud compute ssh "$username@$VM_NAME" \
                --zone="$ZONE" \
                --project="$PROJECT_ID" \
                --command="cd ${remote_path}teamsphere/frontend && tail -f *.log"
            ;;
        *)
            print_error "Unknown service: $service"
            exit 1
            ;;
    esac
}

# Function to show help
show_help() {
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  backend [username] [remote_path]    - Deploy backend only"
    echo "  frontend [username] [remote_path]   - Deploy frontend only"
    echo "  full [username] [remote_path]       - Deploy full project"
    echo "  status [username]                   - Check service status"
    echo "  logs [username] [service] [remote_path] - View logs (service: backend|frontend)"
    echo "  help                                - Show this help"
    echo ""
    echo "Examples:"
    echo "  $0 backend vbs_tms                 - Deploy backend to vbs_tms user"
    echo "  $0 full piyush_mishra              - Deploy full project to piyush_mishra user"
    echo "  $0 logs vbs_tms backend            - View backend logs"
}

# Main script logic
main() {
    local command="${1:-help}"
    
    # Check if VM connection script exists
    if [ ! -f "./connect-vm.sh" ]; then
        print_error "connect-vm.sh not found. Please ensure it's in the same directory."
        exit 1
    fi
    
    # Ensure VM is running
    ./connect-vm.sh status > /dev/null
    
    case "$command" in
        "backend")
            deploy_backend "$2" "$3"
            ;;
        "frontend")
            deploy_frontend "$2" "$3"
            ;;
        "full")
            deploy_full "$2" "$3"
            ;;
        "status")
            check_services "$2"
            ;;
        "logs")
            view_logs "$2" "$3" "$4"
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "Unknown command: $command"
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
