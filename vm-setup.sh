#!/bin/bash

# Virtual Machine Setup Script for Device Identity Isolation
# This script sets up VirtualBox VMs for isolated Augment sessions

set -e

echo "🖥️  Setting up Virtual Machine for Device Identity Isolation..."

# Check if VirtualBox is installed
if ! command -v vboxmanage >/dev/null 2>&1; then
    echo "❌ VirtualBox not found. Installing..."
    
    # Update package list
    sudo apt update
    
    # Install VirtualBox
    sudo apt install -y virtualbox virtualbox-ext-pack
    
    # Add user to vboxusers group
    sudo usermod -a -G vboxusers $USER
    echo "✅ VirtualBox installed. Please log out and log back in."
fi

# VM Configuration
VM_NAME="AugmentSession-$(date +%Y%m%d-%H%M%S)"
VM_MEMORY=2048  # 2GB RAM
VM_DISK_SIZE=20480  # 20GB
ISO_PATH="$HOME/Downloads/ubuntu-22.04-desktop-amd64.iso"

echo "📦 Creating VM: $VM_NAME"

# Create VM
vboxmanage createvm --name "$VM_NAME" --ostype "Ubuntu_64" --register

# Configure VM
vboxmanage modifyvm "$VM_NAME" \
    --memory $VM_MEMORY \
    --cpus 2 \
    --vram 128 \
    --graphicscontroller vmsvga \
    --audio pulse \
    --audiocontroller hda \
    --nic1 nat \
    --natpf1 "ssh,tcp,,2222,,22"

# Create virtual hard disk
vboxmanage createhd --filename "$HOME/VirtualBox VMs/$VM_NAME/$VM_NAME.vdi" --size $VM_DISK_SIZE --format VDI

# Attach storage
vboxmanage storagectl "$VM_NAME" --name "SATA Controller" --add sata --controller IntelAhci
vboxmanage storageattach "$VM_NAME" --storagectl "SATA Controller" --port 0 --device 0 --type hdd --medium "$HOME/VirtualBox VMs/$VM_NAME/$VM_NAME.vdi"

# Check if ISO exists
if [ ! -f "$ISO_PATH" ]; then
    echo "📥 Downloading Ubuntu ISO..."
    wget -O "$ISO_PATH" "https://releases.ubuntu.com/22.04/ubuntu-22.04.3-desktop-amd64.iso"
fi

# Attach ISO
vboxmanage storagectl "$VM_NAME" --name "IDE Controller" --add ide
vboxmanage storageattach "$VM_NAME" --storagectl "IDE Controller" --port 0 --device 0 --type dvddrive --medium "$ISO_PATH"

# Enable hardware virtualization features
vboxmanage modifyvm "$VM_NAME" \
    --hwvirtex on \
    --nestedpaging on \
    --largepages on \
    --vtxvpid on \
    --accelerate3d on

echo "✅ VM '$VM_NAME' created successfully!"
echo ""
echo "🚀 To start the VM:"
echo "vboxmanage startvm '$VM_NAME' --type gui"
echo ""
echo "🔧 Post-installation setup:"
echo "1. Install Ubuntu in the VM"
echo "2. Install Guest Additions for better performance"
echo "3. Install browsers and VS Code in the VM"
echo "4. Use VM snapshots to quickly reset to clean state"

# Create VM management script
cat > vm-manager.sh << 'EOF'
#!/bin/bash

# VM Manager Script
VM_NAME_PREFIX="AugmentSession"

case "$1" in
    "create")
        echo "Creating new VM..."
        ./vm-setup.sh
        ;;
    "list")
        echo "Available VMs:"
        vboxmanage list vms | grep "$VM_NAME_PREFIX"
        ;;
    "start")
        if [ -z "$2" ]; then
            echo "Usage: $0 start <vm-name>"
            exit 1
        fi
        vboxmanage startvm "$2" --type gui
        ;;
    "stop")
        if [ -z "$2" ]; then
            echo "Usage: $0 stop <vm-name>"
            exit 1
        fi
        vboxmanage controlvm "$2" poweroff
        ;;
    "delete")
        if [ -z "$2" ]; then
            echo "Usage: $0 delete <vm-name>"
            exit 1
        fi
        vboxmanage unregistervm "$2" --delete
        ;;
    "snapshot")
        if [ -z "$2" ] || [ -z "$3" ]; then
            echo "Usage: $0 snapshot <vm-name> <snapshot-name>"
            exit 1
        fi
        vboxmanage snapshot "$2" take "$3"
        ;;
    "restore")
        if [ -z "$2" ] || [ -z "$3" ]; then
            echo "Usage: $0 restore <vm-name> <snapshot-name>"
            exit 1
        fi
        vboxmanage snapshot "$2" restore "$3"
        ;;
    *)
        echo "Usage: $0 {create|list|start|stop|delete|snapshot|restore}"
        echo ""
        echo "Examples:"
        echo "  $0 create                           # Create new VM"
        echo "  $0 list                            # List all VMs"
        echo "  $0 start AugmentSession-20231201   # Start specific VM"
        echo "  $0 snapshot VM-name clean-state    # Create snapshot"
        echo "  $0 restore VM-name clean-state     # Restore snapshot"
        exit 1
        ;;
esac
EOF

chmod +x vm-manager.sh
echo "📝 Created vm-manager.sh for easy VM management"
