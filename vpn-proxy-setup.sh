#!/bin/bash

# VPN/Proxy Setup Script for IP Address Masking
# This script sets up various VPN and proxy solutions

set -e

echo "🌐 Setting up VPN/Proxy for IP Address Masking..."

# Function to install OpenVPN
install_openvpn() {
    echo "📦 Installing OpenVPN..."
    sudo apt update
    sudo apt install -y openvpn network-manager-openvpn network-manager-openvpn-gnome
    
    echo "✅ OpenVPN installed!"
    echo "📝 To use:"
    echo "1. Download .ovpn config files from your VPN provider"
    echo "2. Import via Network Manager or use: sudo openvpn --config your-config.ovpn"
}

# Function to install WireGuard
install_wireguard() {
    echo "📦 Installing WireGuard..."
    sudo apt update
    sudo apt install -y wireguard wireguard-tools
    
    echo "✅ WireGuard installed!"
    echo "📝 To use:"
    echo "1. Get WireGuard config from your VPN provider"
    echo "2. Save to /etc/wireguard/wg0.conf"
    echo "3. Start with: sudo wg-quick up wg0"
}

# Function to setup Tor proxy
install_tor() {
    echo "📦 Installing Tor..."
    sudo apt update
    sudo apt install -y tor torbrowser-launcher
    
    # Configure Tor
    sudo tee /etc/tor/torrc << 'EOF'
SocksPort 9050
ControlPort 9051
CookieAuthentication 1
EOF

    sudo systemctl enable tor
    sudo systemctl start tor
    
    echo "✅ Tor installed and configured!"
    echo "📝 SOCKS proxy available at: 127.0.0.1:9050"
}

# Function to setup Privoxy (HTTP proxy for Tor)
install_privoxy() {
    echo "📦 Installing Privoxy..."
    sudo apt update
    sudo apt install -y privoxy
    
    # Configure Privoxy to use Tor
    sudo tee -a /etc/privoxy/config << 'EOF'

# Forward to Tor
forward-socks5 / 127.0.0.1:9050 .
EOF

    sudo systemctl enable privoxy
    sudo systemctl restart privoxy
    
    echo "✅ Privoxy installed and configured!"
    echo "📝 HTTP proxy available at: 127.0.0.1:8118"
}

# Function to create proxy rotation script
create_proxy_rotator() {
    cat > proxy-rotator.sh << 'EOF'
#!/bin/bash

# Proxy Rotation Script
# Rotates between different proxy configurations

PROXIES=(
    "127.0.0.1:8118"     # Privoxy/Tor
    "127.0.0.1:3128"     # Squid (if installed)
    "socks5://127.0.0.1:9050"  # Tor SOCKS
)

VPN_CONFIGS=(
    "/etc/wireguard/wg0.conf"
    "/etc/openvpn/client/config1.ovpn"
    "/etc/openvpn/client/config2.ovpn"
)

# Function to set system proxy
set_system_proxy() {
    local proxy=$1
    echo "🔄 Setting system proxy to: $proxy"
    
    gsettings set org.gnome.system.proxy mode 'manual'
    gsettings set org.gnome.system.proxy.http host "${proxy%:*}"
    gsettings set org.gnome.system.proxy.http port "${proxy#*:}"
    gsettings set org.gnome.system.proxy.https host "${proxy%:*}"
    gsettings set org.gnome.system.proxy.https port "${proxy#*:}"
}

# Function to disable system proxy
disable_system_proxy() {
    echo "❌ Disabling system proxy"
    gsettings set org.gnome.system.proxy mode 'none'
}

# Function to rotate VPN
rotate_vpn() {
    echo "🔄 Rotating VPN connection..."
    
    # Disconnect current VPN
    sudo wg-quick down wg0 2>/dev/null || true
    sudo pkill openvpn 2>/dev/null || true
    
    # Select random VPN config
    local config=${VPN_CONFIGS[$RANDOM % ${#VPN_CONFIGS[@]}]}
    
    if [[ $config == *".conf" ]] && [ -f "$config" ]; then
        echo "🔌 Connecting to WireGuard: $config"
        sudo wg-quick up "${config##*/}"
    elif [[ $config == *".ovpn" ]] && [ -f "$config" ]; then
        echo "🔌 Connecting to OpenVPN: $config"
        sudo openvpn --config "$config" --daemon
    fi
}

# Function to rotate proxy
rotate_proxy() {
    local proxy=${PROXIES[$RANDOM % ${#PROXIES[@]}]}
    set_system_proxy "$proxy"
}

# Function to get current IP
get_current_ip() {
    echo "🌍 Current IP address:"
    curl -s https://ipinfo.io/ip || curl -s https://icanhazip.com || echo "Unable to fetch IP"
}

case "$1" in
    "rotate-vpn")
        rotate_vpn
        sleep 5
        get_current_ip
        ;;
    "rotate-proxy")
        rotate_proxy
        get_current_ip
        ;;
    "disable-proxy")
        disable_system_proxy
        ;;
    "status")
        get_current_ip
        echo ""
        echo "Active VPN connections:"
        sudo wg show 2>/dev/null || echo "No WireGuard connections"
        pgrep openvpn >/dev/null && echo "OpenVPN running" || echo "No OpenVPN connections"
        ;;
    "auto-rotate")
        echo "🔄 Starting auto-rotation (every 5 minutes)..."
        while true; do
            rotate_vpn
            sleep 300  # 5 minutes
            rotate_proxy
            sleep 300  # 5 minutes
        done
        ;;
    *)
        echo "Usage: $0 {rotate-vpn|rotate-proxy|disable-proxy|status|auto-rotate}"
        echo ""
        echo "Examples:"
        echo "  $0 rotate-vpn     # Switch to random VPN"
        echo "  $0 rotate-proxy   # Switch to random proxy"
        echo "  $0 status         # Show current IP and connections"
        echo "  $0 auto-rotate    # Auto-rotate every 5 minutes"
        exit 1
        ;;
esac
EOF

    chmod +x proxy-rotator.sh
    echo "✅ Proxy rotator script created!"
}

# Function to setup browser proxy profiles
create_browser_profiles() {
    cat > browser-proxy-launcher.sh << 'EOF'
#!/bin/bash

# Browser Proxy Launcher
# Launches browsers with different proxy configurations

launch_chrome_with_proxy() {
    local proxy=$1
    local profile_dir="/tmp/chrome-proxy-$(date +%s)"
    
    google-chrome \
        --user-data-dir="$profile_dir" \
        --proxy-server="$proxy" \
        --disable-web-security \
        --disable-features=VizDisplayCompositor \
        --incognito &
}

launch_firefox_with_proxy() {
    local proxy=$1
    local profile_dir="/tmp/firefox-proxy-$(date +%s)"
    
    mkdir -p "$profile_dir"
    
    # Create Firefox profile with proxy settings
    cat > "$profile_dir/user.js" << EOL
user_pref("network.proxy.type", 1);
user_pref("network.proxy.http", "${proxy%:*}");
user_pref("network.proxy.http_port", ${proxy#*:});
user_pref("network.proxy.ssl", "${proxy%:*}");
user_pref("network.proxy.ssl_port", ${proxy#*:});
user_pref("network.proxy.share_proxy_settings", true);
user_pref("privacy.trackingprotection.enabled", true);
user_pref("privacy.donottrackheader.enabled", true);
EOL

    firefox --profile "$profile_dir" --private-window &
}

case "$1" in
    "chrome")
        proxy=${2:-"127.0.0.1:8118"}
        echo "🚀 Launching Chrome with proxy: $proxy"
        launch_chrome_with_proxy "$proxy"
        ;;
    "firefox")
        proxy=${2:-"127.0.0.1:8118"}
        echo "🚀 Launching Firefox with proxy: $proxy"
        launch_firefox_with_proxy "$proxy"
        ;;
    *)
        echo "Usage: $0 {chrome|firefox} [proxy]"
        echo ""
        echo "Examples:"
        echo "  $0 chrome 127.0.0.1:8118"
        echo "  $0 firefox 127.0.0.1:9050"
        exit 1
        ;;
esac
EOF

    chmod +x browser-proxy-launcher.sh
    echo "✅ Browser proxy launcher created!"
}

# Main installation menu
echo "🔧 VPN/Proxy Setup Options:"
echo "1. Install OpenVPN"
echo "2. Install WireGuard"
echo "3. Install Tor + Privoxy"
echo "4. Create proxy rotation scripts"
echo "5. Install all"
echo ""
read -p "Choose option (1-5): " choice

case $choice in
    1)
        install_openvpn
        ;;
    2)
        install_wireguard
        ;;
    3)
        install_tor
        install_privoxy
        ;;
    4)
        create_proxy_rotator
        create_browser_profiles
        ;;
    5)
        install_openvpn
        install_wireguard
        install_tor
        install_privoxy
        create_proxy_rotator
        create_browser_profiles
        ;;
    *)
        echo "❌ Invalid option"
        exit 1
        ;;
esac

echo ""
echo "✅ VPN/Proxy setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Configure VPN credentials from your provider"
echo "2. Test connections with: ./proxy-rotator.sh status"
echo "3. Use browser launchers: ./browser-proxy-launcher.sh chrome"
