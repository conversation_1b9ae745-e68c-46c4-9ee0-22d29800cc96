package com.vbs.capsAllocation.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vbs.capsAllocation.dto.BaseResponse;
import com.vbs.capsAllocation.dto.LeaveBalanceUploadDto;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.vbs.capsAllocation.dto.VunnoMgmtDto;
import com.vbs.capsAllocation.dto.VunnoRequestDto;
import com.vbs.capsAllocation.model.*;
import com.vbs.capsAllocation.repository.*;
import com.vbs.capsAllocation.util.EmailTemplateUtil;
import com.vbs.capsAllocation.util.LoggerUtil;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.*;

import java.nio.charset.StandardCharsets;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.server.ResponseStatusException;

@Service
public class VunnoMgmtService {

    @Autowired
    private static final String APPLICATION_NAME = "Google Sheets API Java Quickstart";
    private static final String TOKENS_DIRECTORY_PATH = "tokens";

    @Value("${google.sheets.spreadsheetId}")
    private String spreadsheetId;

    @Value("${google.sheets.sheetName}")
    private String sheetName;

    @Autowired
    private VunnoAuditRepository vunnoAuditRepository;

    @Autowired
    private VunnoResponseRepository vunnoResponseRepository;

    @Autowired
    private LeaveBalanceRepository leaveBalanceRepository;

    @Autowired
    private LeaveUsageLogRepository leaveUsageLogRepository;

    @Autowired
    private EmailService emailService;

    @Autowired
    private ApprovalTokenService approvalTokenService;

    @Autowired
    private EmailTemplateUtil emailTemplateUtil;

    @Autowired
    private EmployeeRepository employeeRepository;

    private static final Logger logger = LoggerFactory.getLogger(VunnoMgmtService.class);

    public List<Map<String, String>> getFilteredSheetData(String ldap) {
        List<VunnoResponse> responses = vunnoResponseRepository.findByEmployeeLdap(ldap);

        if (responses.isEmpty()) {
            logger.info("No records found in database for LDAP: {}", ldap);
            return Collections.emptyList();
        }

        List<Map<String, String>> filteredData = new ArrayList<>();

        for (VunnoResponse response : responses) {
            Map<String, String> rowData = new HashMap<>();
            rowData.put("Status", response.getStatus());
            rowData.put("Requestor Name",response.getRequestorName());
            rowData.put("Request Type", response.getApplicationType());
            rowData.put("Leave Type", response.getLeaveType());
            rowData.put("Start Date", response.getFromDate() != null ? response.getFromDate().toString() : "");
            rowData.put("End Date", response.getToDate() != null ? response.getToDate().toString() : "");
            rowData.put("Duration", response.getDuration() != null ? response.getDuration() : "");
            rowData.put("Approver", response.getApprover());

            filteredData.add(rowData);
        }

        logger.info("Returning {} records for LDAP: {}",  ldap);
        return filteredData;
    }

    public String uploadLeaveBalances(MultipartFile file, UserDetails userDetails, boolean force) {
        String uploader = userDetails.getUsername();
        Integer year = Year.now().getValue();
        Integer month = LocalDate.now().getMonthValue();

        boolean alreadyUploadedThisMonth = leaveBalanceRepository.existsByMonthAndYear(month, year);

        String existingUploader = leaveBalanceRepository.findUploaderForThisMonth().orElse("Unknown");

        if (!force && alreadyUploadedThisMonth && !existingUploader.equals(uploader)) {
            throw new IllegalStateException("Leave balances already uploaded this month by " + existingUploader + ".");
        }

        List<LeaveBalanceUploadDto> rows = parseLeaveBalanceSheet(file);
        for (LeaveBalanceUploadDto row : rows) {
            Optional<Employee> empOpt = employeeRepository.findByLdap(row.getLdap().trim());
            if (empOpt.isEmpty()) continue;

            Employee employee = empOpt.get();
            saveOrUpdateBalance(employee, "HR-SL", row.getSlBalance(), year, month, uploader);
            saveOrUpdateBalance(employee, "HR-CL", row.getClBalance(), year, month, uploader);
            saveOrUpdateBalance(employee, "HR-EL", row.getElBalance(), year, month, uploader);
        }

        return "Leave balances uploaded successfully.";
    }

    private List<LeaveBalanceUploadDto> parseLeaveBalanceSheet(MultipartFile file) {
        List<LeaveBalanceUploadDto> records = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
            CSVParser parser = CSVFormat.DEFAULT
                    .withFirstRecordAsHeader()
                    .withIgnoreHeaderCase()
                    .withTrim()
                    .parse(reader);

            String currentMonth = LocalDate.now().getMonth().getDisplayName(TextStyle.SHORT, Locale.ENGLISH); // e.g. "Jul"

            for (CSVRecord record : parser) {
                String ldap = record.get("ldap/email").trim();
                if (ldap.isEmpty()) continue;

                LeaveBalanceUploadDto dto = new LeaveBalanceUploadDto();
                dto.setLdap(ldap);
                dto.setSlBalance(parseDoubleSafe(record.get(currentMonth + "-SL-Balance")));
                dto.setClBalance(parseDoubleSafe(record.get(currentMonth + "-CL-Balance")));
                dto.setElBalance(parseDoubleSafe(record.get(currentMonth + "-EL-Balance")));

                records.add(dto);
            }
        } catch (IOException e) {
            throw new RuntimeException("Failed to parse leave balance sheet", e);
        }

        return records;
    }

    private Double parseDoubleSafe(String val) {
        try {
            return (Double) (val == null || val.trim().isEmpty() ? 0.0 : Double.parseDouble(val.trim()));
        } catch (NumberFormatException e) {
            return (Double) 0.0;
        }
    }

    private void saveOrUpdateBalance(Employee employee, String leaveType, Double balance, Integer year, Integer month, String uploader) {
        Optional<LeaveBalance> existingOpt = leaveBalanceRepository
                .findByEmployeeAndLeaveTypeAndMonthAndYear(employee, leaveType, month, year);

        LeaveBalance balanceEntity = existingOpt.orElseGet(LeaveBalance::new);
        balanceEntity.setEmployee(employee);
        balanceEntity.setLeaveType(leaveType);
        balanceEntity.setBalance(balance);
        balanceEntity.setYear(year);
        balanceEntity.setMonth(month);
        balanceEntity.setSource("HR_UPLOAD");
        balanceEntity.setUploadedBy(uploader);
        balanceEntity.setUploadedAt(LocalDateTime.now());
        balanceEntity.setEffectiveFrom(LocalDate.of(year, month, 1));
        leaveBalanceRepository.save(balanceEntity);
    }


    public String setRequestedVunno(VunnoRequestDto requestDto) {
        logger.info("setRequestedVunno function of VunnoMgmtService Class Started");

        String returnValue = "";

        if(requestDto.getStatus().equalsIgnoreCase("PENDING")){
            logger.info("Processing request for approval");
            returnValue = requestingLeave(requestDto);
        }
        else {
            return "Error in processing your request";
        }

        logger.info("setRequestedVunno function of VunnoMgmtService Class Ended");
        return returnValue;
    }

    // Helper method
    private String getQuarter(LocalDate date) {
        int month = date.getMonthValue();
        if(month <= 3) return "Q1";
        else if(month <= 6) return "Q2";
        else if(month <= 9) return "Q3";
        else return "Q4";
    }

    public String requestingLeave(VunnoRequestDto requestDto) {
        try {
            logger.info("Fetching Details of lead-Manager");
            List<VunnoMgmtDto> MgmtDetails = getLeadManagerDetails(requestDto.getLdap());

            if (MgmtDetails == null || MgmtDetails.isEmpty()) {
                throw new RuntimeException("Lead manager details not found for LDAP: " + requestDto.getLdap());
            }

            VunnoMgmtDto dto = MgmtDetails.get(0);
            String program = dto.getProgramAlignment();
            String team = dto.getTeam();
            String name = dto.getName();

            final String DOMAIN = "@google.com";
            String approvingLead = requestDto.getApprovingLead();
            String requester = requestDto.getLdap() + DOMAIN;
            String ldap = requestDto.getLdap();
            String applicationType = requestDto.getApplicationType();
            String leaveType = "Leave".equalsIgnoreCase(applicationType) ? requestDto.getLeaveType() : "";
            String lvwfhDuration = requestDto.getLvWfhDuration();
            String backupInfo = requestDto.getBackupInfo();
            String oooProof = requestDto.getOooProof();
            String timesheetProof = requestDto.getTimesheetProof();

            // Format dates
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate fromDate = LocalDate.parse(requestDto.getStartDate(), dateFormatter);
            LocalDate toDate = LocalDate.parse(requestDto.getEndDate(), dateFormatter);

            // Fetch Employee
            Employee employee = employeeRepository.findByLdap(ldap)
                    .orElseThrow(() -> new RuntimeException("Employee not found for LDAP: " + ldap));

            // Create VunnoResponse entity
            VunnoResponse response = new VunnoResponse();
            response.setTimestamp(LocalDateTime.now());
            response.setTeam(team);
            response.setRequestorName(name);
            response.setApprover(approvingLead);
            response.setFromDate(fromDate);
            response.setToDate(toDate);
            response.setApplicationType(applicationType);
            response.setLeaveType(leaveType);
            response.setDuration(lvwfhDuration);
            response.setProgram(program);
            response.setStatus(requestDto.getStatus()); // Should be "PENDING"
            response.setBackup(backupInfo);
            response.setOrgScreenshot(oooProof);
            response.setReason(requestDto.getReason()); // ✅ save mandatory reason
            response.setDocumentPath(requestDto.getDocumentPath());
            response.setTimesheetScreenshot(timesheetProof);
            response.setEmployee(employee);

            // Save to database
            vunnoResponseRepository.save(response);

            logger.info("Leave request saved successfully for " + ldap);
            return "Requested leave successfully saved into database";

        } catch (Exception e) {
            logger.error("An error occurred in requestingLeave method", e);
            return "An error occurred while processing the request: " + e.getMessage();
        }
    }

    // Fetch Leads Information when passed Ldap
    public List<VunnoMgmtDto> getLeadManagerDetails(String ldap) {
        if (ldap == null || ldap.isEmpty()) {
            throw new IllegalArgumentException("LDAP Cannot be Empty");
        }

        Employee employee = employeeRepository.findByLdap(ldap)
                .orElseThrow(() -> new RuntimeException("Employee not found"));

        String fullName = employee.getFirstName() + " " + employee.getLastName();

        return Collections.singletonList(new VunnoMgmtDto(
                employee.getLdap(),
                fullName,
                employee.getLevel(),
                employee.getEmail(),
                employee.getPnseProgram(),
                employee.getTeam(),
                employee.getLead(),
                employee.getProgramManager(),
                employee.getShift()
        ));
    }

    public List<Double> getAllCountOfLeaveWFH(String ldap) {
        int currentYear = Year.now().getValue();
        String currentQuarter = getCurrentQuarter();

        double finalSL = calculateEffectiveBalance(ldap, "HR-SL", "SL");
        double finalCL = calculateEffectiveBalance(ldap, "HR-CL", "CL");
        double finalEL = calculateEffectiveBalance(ldap, "HR-EL", "EL");

        // Total WFH used
        double totalWFH = leaveUsageLogRepository.sumDaysTakenByLdapAndLeaveType(ldap, "WFH");

        // WFH used in current quarter
        double quarterlyWFH = leaveUsageLogRepository.sumDaysTakenByLdapAndLeaveTypeAndQuarter(
                ldap, "WFH", currentQuarter, String.valueOf(currentYear)
        );

        return List.of(finalSL, finalCL, finalEL, finalSL + finalCL + finalEL, totalWFH, quarterlyWFH);
    }

    private double calculateEffectiveBalance(String ldap, String hrType, String usageType) {
        // Step 1: Find latest HR-uploaded balance for this leave type
        Optional<LeaveBalance> latestBalanceOpt = leaveBalanceRepository
                .findTopByEmployee_LdapAndLeaveTypeOrderByEffectiveFromDesc(ldap, hrType);

        if (latestBalanceOpt.isEmpty()) return 0;

        LeaveBalance latestBalance = latestBalanceOpt.get();
        double hrBalance = latestBalance.getBalance();
        LocalDate effectiveFrom = latestBalance.getEffectiveFrom();

        // Step 2: Sum usage from usage logs only after effectiveFrom
        Double used = leaveUsageLogRepository
                .sumDaysTakenByLdapAndLeaveTypeSinceDate(ldap, usageType, effectiveFrom);

        double usedLeave = used != null ? used : 0;

        // Step 3: Subtract
        return Math.max(0, hrBalance - usedLeave);
    }

    // Helper method to get current quarter as a string (Q1, Q2, Q3, or Q4)
    public String getCurrentQuarter() {
        LocalDate date = LocalDate.now();
        int month = date.getMonthValue();
        if (month >= 1 && month <= 3) {
            return "Q1";
        } else if (month >= 4 && month <= 6) {
            return "Q2";
        } else if (month >= 7 && month <= 9) {
            return "Q3";
        } else {
            return "Q4";
        }

    }

    public String deleteLeaveRequestWithPermission(Long id, UserDetails userDetails) {
        String loggedInUser = userDetails.getUsername();
        List<String> roles = userDetails.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());

        Optional<VunnoResponse> optional = vunnoResponseRepository.findById(id);
        if (optional.isEmpty()) {
            throw new IllegalArgumentException("Leave request not found.");
        }

        VunnoResponse request = optional.get();

        request.setStatus("DELETED");
        request.setDeletedAt(LocalDateTime.now());
        vunnoResponseRepository.save(request);

        boolean auditLogged = logDeleteAction(request, loggedInUser, roles);
        if (!auditLogged) {
            throw new IllegalStateException("Failed to log audit for deletion.");
        }
        return "Leave Request Successfully Deleted!";
    }

    public boolean logDeleteAction(VunnoResponse request, String loggedInUser, List<String> roles) {
        try {
            System.out.println("ROLES DELETE " + roles);
            VunnoAuditLog auditLog = new VunnoAuditLog();
            auditLog.setVunnoResponseId(request.getId());
            auditLog.setActionType("DELETE");
            auditLog.setPreviousStatus("PENDING");
            auditLog.setNewStatus("DELETED");
            auditLog.setChangedBy(loggedInUser);
            auditLog.setChangedByRole(String.join(",", roles));
            auditLog.setChangedAt(LocalDateTime.now());
            auditLog.setChangeDescription("Deleted by LoggedIn User");

            vunnoAuditRepository.save(auditLog);
            return true;
        } catch (Exception e) {
            LoggerUtil.logError("Error logging audit record: {}", e.getMessage(), e);
            return false;
        }
    }

    public String updateLeaveRequestWithPermission(Long id, VunnoRequestDto updateRequest, UserDetails userDetails) {
        String loggedInUser = userDetails.getUsername();

        Optional<VunnoResponse> optional = vunnoResponseRepository.findById(id);
        if (optional.isEmpty()) {
            throw new IllegalArgumentException("Leave request not found.");
        }

        System.out.println("UpdatedRequest " + updateRequest);

        VunnoResponse request = optional.get();

        // Update fields based on VunnoRequestDto
        request.setFromDate(OffsetDateTime.parse(updateRequest.getStartDate()).toLocalDate());
        request.setToDate(OffsetDateTime.parse(updateRequest.getEndDate()).toLocalDate());

        // Application type and duration
        request.setApplicationType(updateRequest.getApplicationType());
        request.setDuration(updateRequest.getLvWfhDuration());

        // Leave type logic
        if ("Leave".equalsIgnoreCase(updateRequest.getApplicationType())) {
            request.setLeaveType(updateRequest.getLeaveType());
        } else {
            request.setLeaveType(null);
        }

        if (updateRequest.getStatus() != null) {
            request.setStatus(updateRequest.getStatus());
        }
        if (updateRequest.getApprovingLead() != null) {
            request.setApprover(updateRequest.getApprovingLead());
        }
        // Optionally update other fields (e.g., backup info, ooo proof, timesheet) if needed
        request.setBackup(updateRequest.getBackupInfo());

        vunnoResponseRepository.save(request);

        // Audit log
        boolean auditLogged = logUpdateAction(
                request,
                loggedInUser,
                userDetails.getAuthorities().stream().map(GrantedAuthority::getAuthority).collect(Collectors.toList()),
                "Leave request updated"
        );

        if (!auditLogged) {
            throw new IllegalStateException("Failed to log audit for update.");
        }

        return "Leave request updated successfully.";
    }

    public boolean logUpdateAction(
            VunnoResponse updatedRequest,
            String changedBy,
            List<String> roles,
            String reason
    )
    {
        try {
            // Fetch the previous state from DB before update
            Optional<VunnoResponse> existingOptional = vunnoResponseRepository.findById(updatedRequest.getId());
            if (existingOptional.isEmpty()) {
                return false;
            }

            VunnoResponse previous = existingOptional.get();

            // Prepare previous and new values as JSON-like maps
            Map<String, Object> previousValues = new HashMap<>();
            Map<String, Object> newValues = new HashMap<>();

            previousValues.put("fromDate", previous.getFromDate());
            previousValues.put("toDate", previous.getToDate());
            previousValues.put("applicationType", previous.getApplicationType());
            previousValues.put("duration", previous.getDuration());
            previousValues.put("leaveType", previous.getLeaveType());
            previousValues.put("backup", previous.getBackup());

            newValues.put("fromDate", updatedRequest.getFromDate());
            newValues.put("toDate", updatedRequest.getToDate());
            newValues.put("applicationType", updatedRequest.getApplicationType());
            newValues.put("duration", updatedRequest.getDuration());
            newValues.put("leaveType", updatedRequest.getLeaveType());
            newValues.put("backup", updatedRequest.getBackup());

            // Convert maps to JSON strings
            ObjectMapper mapper = new ObjectMapper();
            mapper.registerModule(new JavaTimeModule());

            String previousJson = mapper.writeValueAsString(previousValues);
            String newJson = mapper.writeValueAsString(newValues);

            VunnoAuditLog auditLog = new VunnoAuditLog();
            auditLog.setVunnoResponseId(updatedRequest.getId());
            auditLog.setActionType("EDIT");
            auditLog.setPreviousStatus("PENDING");
            auditLog.setNewStatus(updatedRequest.getStatus());
            auditLog.setChangedBy(changedBy);
            auditLog.setChangedByRole(String.join(",", roles));
            auditLog.setChangedAt(LocalDateTime.now());
            auditLog.setChangeReason(reason);
            auditLog.setPreviousValues(previousJson);
            auditLog.setNewValues(newJson);


            vunnoAuditRepository.save(auditLog);
            return true;

        } catch (Exception e) {
            LoggerUtil.logError("Error logging update audit record: {}", e.getMessage(), e);
            return false;
        }
    }

    @Transactional(readOnly = true)
    public List<VunnoRequestDto> getApprovedRequests(UserDetails userDetails) {
        String ldap = userDetails.getUsername();
        List<String> roles = userDetails.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());

        List<String> targetLdaps = new ArrayList<>();

        if (roles.contains("ROLE_ADMIN_OPS_MANAGER")) {
            targetLdaps = employeeRepository.findAllLdaps();
        } else if (roles.contains("ROLE_MANAGER")) {
            targetLdaps = employeeRepository.findLdapsByManager(ldap);
        } else if (roles.contains("ROLE_LEAD")) {
            targetLdaps = employeeRepository.findLdapsByLead(ldap);
        }

        if (targetLdaps.isEmpty()) {
            return Collections.emptyList();
        }

        List<VunnoResponse> responses = vunnoResponseRepository.findApprovedRequestsByTeamLdaps(targetLdaps);

        return responses.stream().map(r -> {
            Employee emp = r.getEmployee();
            String requestorFullName = emp != null ? emp.getFirstName() + " " + emp.getLastName() : "";

            return new VunnoRequestDto(
                    r.getId(),
                    emp != null ? emp.getLdap() : null,
                    r.getApprover(),
                    r.getApplicationType(),
                    r.getLeaveType(),
                    r.getDuration(),
                    r.getFromDate() != null ? r.getFromDate().toString() : null,
                    r.getToDate() != null ? r.getToDate().toString() : null,
                    null,
                    null,
                    r.getBackup(),
                    r.getOrgScreenshot(),
                    r.getTimesheetScreenshot(),
                    r.getStatus(),
                    requestorFullName,
                    r.getReason(),
                    r.getDocumentPath()
            );
        }).collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<VunnoRequestDto> getRequestsForApproval(UserDetails userDetails) {
        String ldap = userDetails.getUsername();
        List<String> roles = userDetails.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());

        List<String> targetLdaps = new ArrayList<>();
        boolean isLead = false;

        if (roles.contains("ROLE_ADMIN_OPS_MANAGER")) {
            targetLdaps = employeeRepository.findAllLdaps();
        } else if (roles.contains("ROLE_MANAGER")) {
            targetLdaps = employeeRepository.findLdapsByManager(ldap);
        } else if (roles.contains("ROLE_LEAD")) {
            targetLdaps = employeeRepository.findLdapsByLead(ldap);
            isLead = true;
        }

        if (targetLdaps.isEmpty()) {
            return Collections.emptyList();
        }

        List<VunnoResponse> responses = vunnoResponseRepository.findPendingRequestsByTeamLdaps(targetLdaps);

        // ❌ Filter out WFH requests if the user is a Lead
        if (isLead) {
            responses = responses.stream()
                    .filter(r -> !"Work From Home".equalsIgnoreCase(r.getApplicationType()))
                    .toList();
        }

        return responses.stream().map(r -> {
            Employee emp = r.getEmployee();
            String requestorFullName = emp != null ? emp.getFirstName() + " " + emp.getLastName() : "";

            return new VunnoRequestDto(
                    r.getId(),
                    emp != null ? emp.getLdap() : null,
                    r.getApprover(),
                    r.getApplicationType(),
                    r.getLeaveType(),
                    r.getDuration(),
                    r.getFromDate() != null ? r.getFromDate().toString() : null,
                    r.getToDate() != null ? r.getToDate().toString() : null,
                    null,
                    null,
                    r.getBackup(),
                    r.getOrgScreenshot(),
                    r.getTimesheetScreenshot(),
                    r.getStatus(),
                    requestorFullName,
                    r.getReason(),
                    r.getDocumentPath()
            );
        }).collect(Collectors.toList());
    }

    private String getRoleFromUserDetails(UserDetails userDetails) {
        return userDetails.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .findFirst()
                .orElse("UNKNOWN");
    }

    @Transactional
    public String rejectRequest(VunnoRequestDto requestDto, UserDetails userDetails) {

        Long requestId = requestDto.getId();
        String approverLdap = userDetails.getUsername();
        String role = getRoleFromUserDetails(userDetails);

        VunnoResponse response = vunnoResponseRepository.findById(requestId)
                .orElseThrow(() -> new RuntimeException("Request not found with ID: " + requestId));

        String previousStatus = response.getStatus();
        if (!"PENDING".equalsIgnoreCase(previousStatus)) {
            return "Only PENDING requests can be rejected.";
        }

        response.setStatus("REJECTED");
        response.setApprover(approverLdap);
        vunnoResponseRepository.save(response);

        logAudit(response, "REJECT", "REJECTED", approverLdap, role, previousStatus, "Request rejected by " + role);

        return "Request rejected successfully.";
    }

    @Transactional
    public String revokeRequest(VunnoRequestDto requestDto, UserDetails userDetails) {
        Long requestId = requestDto.getId();
        String revokerLdap = userDetails.getUsername();
        String role = getRoleFromUserDetails(userDetails);

        VunnoResponse response = vunnoResponseRepository.findById(requestId)
                .orElseThrow(() -> new RuntimeException("Request not found with ID: " + requestId));

        String currentStatus = response.getStatus();
        if (!"APPROVED".equalsIgnoreCase(currentStatus)) {
            return "Only APPROVED requests can be revoked.";
        }

        if (LocalDate.now().isAfter(response.getToDate())) {
            return "Request cannot be revoked after its end date.";
        }

        revertLeaveOrWfh(response);

        response.setStatus("REVOKED");
        vunnoResponseRepository.save(response);

        logAudit(response, "REVOKE", "REVOKED", revokerLdap, role, currentStatus, "Request revoked by " + role);
        return "Request revoked successfully.";
    }

    @Transactional
    public String approveRequest(VunnoRequestDto requestDto, UserDetails userDetails) {

        Long requestId = requestDto.getId();
        String approverLdap = userDetails.getUsername();
        String role = getRoleFromUserDetails(userDetails);

        VunnoResponse response = vunnoResponseRepository.findById(requestId)
                .orElseThrow(() -> new RuntimeException("Request not found with ID: " + requestId));

        String previousStatus = response.getStatus();
        if (!"PENDING".equalsIgnoreCase(previousStatus)) {
            return "Only PENDING requests can be approved.";
        }

        String toEmail = requestDto.getLdap() + "@google.com";
        String ccEmails = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>";
        String applicationType = response.getApplicationType();
        String backupInfo = response.getBackup();

        String subject = "Request Approved | " + requestDto.getLdap() + " | " +  applicationType +  " | " + response.getFromDate().toString() + "-" + response.getToDate().toString();
        String body = emailTemplateUtil.getVunnoApprovalEmail(
                backupInfo,
                applicationType,
                approverLdap,
                response.getFromDate().toString(),
                response.getToDate().toString(),
                response.getDuration()
        );

        response.setStatus("APPROVED");
        response.setApprover(approverLdap);
        vunnoResponseRepository.save(response);

        // Update leave usage or WFH
        updateUsageOrWfh(response);

        // Log audit
        logAudit(response, "APPROVE", "APPROVED",approverLdap, role, previousStatus, "Request approved by " + role);

        emailService.sendEmail(toEmail, subject, body, ccEmails);

        return "Request approved successfully.";
    }

    @Transactional(readOnly = true)
    public List<VunnoRequestDto> getProcessedRequestsForApproval(UserDetails userDetails, String statusFilter) {
        String ldap = userDetails.getUsername();
        List<String> roles = userDetails.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());

        List<String> targetLdaps = new ArrayList<>();

        boolean isLead = false;
        if (roles.contains("ROLE_ADMIN_OPS_MANAGER")) {
            targetLdaps = employeeRepository.findAllLdaps();
        } else if (roles.contains("ROLE_MANAGER")) {
            targetLdaps = employeeRepository.findLdapsByManager(ldap);
        } else if (roles.contains("ROLE_LEAD")) {
            targetLdaps = employeeRepository.findLdapsByLead(ldap);
            isLead = true;
        }

        if (targetLdaps.isEmpty()) {
            return Collections.emptyList();
        }

        List<String> statuses = statusFilter != null
                ? List.of(statusFilter)
                : List.of("APPROVED", "REJECTED", "REVOKED");

        List<VunnoResponse> responses =
                vunnoResponseRepository.findByEmployeeLdapInAndStatusIn(targetLdaps, statuses);

        // ❌ Leads should NOT see WFH entries
        if (isLead) {
            responses = responses.stream()
                    .filter(r -> !"Work From Home".equalsIgnoreCase(r.getApplicationType()))
                    .toList();
        }

        return responses.stream().map(r -> {
            Employee emp = r.getEmployee();
            String requestorFullName = emp != null ? emp.getFirstName() + " " + emp.getLastName() : "";

            return new VunnoRequestDto(
                    r.getId(),
                    emp != null ? emp.getLdap() : null,
                    r.getApprover(),
                    r.getApplicationType(),
                    r.getLeaveType(),
                    r.getDuration(),
                    r.getFromDate() != null ? r.getFromDate().toString() : null,
                    r.getToDate() != null ? r.getToDate().toString() : null,
                    null,
                    null,
                    r.getBackup(),
                    r.getOrgScreenshot(),
                    r.getTimesheetScreenshot(),
                    r.getStatus(),
                    requestorFullName,
                    r.getReason(),
                    r.getDocumentPath()
            );
        }).collect(Collectors.toList());
    }

    private void updateUsageOrWfh(VunnoResponse response) {
        Employee employee = response.getEmployee();
        String applicationType = response.getApplicationType(); // Leave or Work From Home
        LocalDate fromDate = response.getFromDate();
        LocalDate toDate = response.getToDate();
        int currentYear = fromDate.getYear();
        String currentQuarter = getQuarter(fromDate);

        if ("Leave".equalsIgnoreCase(applicationType)) {
            double totalDaysTaken = calculateDaysTaken(response.getDuration(), fromDate, toDate);

            String leaveType = response.getLeaveType();
            LeaveUsageLog log = new LeaveUsageLog();
            log.setEmployee(employee);
            log.setLeaveType(leaveType);
            log.setDaysTaken(totalDaysTaken);
            log.setLeaveDate(fromDate);
            log.setYear(currentYear);
            log.setQuarter(currentQuarter);

            leaveUsageLogRepository.save(log);
        }
        else if ("Work From Home".equalsIgnoreCase(applicationType)) {
            double daysTaken;

            switch (response.getDuration()) {
                case "Full Day" -> daysTaken = 1.0;
                case "Half Day AM", "Half Day PM" -> daysTaken = 0.5;
                case "Multiple Days" -> {
                    if (fromDate == null || toDate == null) {
                        throw new LeaveBalanceException("FromDate or ToDate missing for Multiple Days WFH.");
                    }
                    long days = ChronoUnit.DAYS.between(fromDate, toDate) + 1;
                    daysTaken = (double) days;
                }
                default -> throw new LeaveBalanceException("Invalid WFH duration: " + response.getDuration());
            }

            LeaveUsageLog wfhLog = new LeaveUsageLog();
            wfhLog.setEmployee(employee);
            wfhLog.setLeaveType("WFH");
            wfhLog.setDaysTaken(daysTaken);
            wfhLog.setLeaveDate(fromDate);
            wfhLog.setYear(currentYear);
            wfhLog.setQuarter(currentQuarter);

            leaveUsageLogRepository.save(wfhLog);
        }
    }


    public class LeaveBalanceException extends RuntimeException {
        public LeaveBalanceException(String message) {
            super(message);
        }
    }


    private double calculateDaysTaken(String duration, LocalDate fromDate, LocalDate toDate) {
        if (duration == null || fromDate == null || toDate == null) {
            throw new LeaveBalanceException("Missing duration or dates for leave calculation.");
        }

        duration = duration.trim().toLowerCase();

        if (duration.contains("half")) {
            return 0.5;
        } else if (duration.contains("full")) {
            return 1.0;
        } else if (duration.contains("multiple")) {
            long days = ChronoUnit.DAYS.between(fromDate, toDate) + 1; // Inclusive
            return (double) days;
        } else {
            throw new LeaveBalanceException("Unknown duration format: " + duration);
        }
    }

    private void revertLeaveOrWfh(VunnoResponse response) {
        Employee employee = response.getEmployee();
        String applicationType = response.getApplicationType();
        LocalDate fromDate = response.getFromDate();
        LocalDate toDate = response.getToDate();
        int currentYear = fromDate.getYear();

        if ("Leave".equalsIgnoreCase(applicationType)) {
            // Identify logs by employee, date range, and year
            List<LeaveUsageLog> logs = leaveUsageLogRepository
                    .findByEmployeeAndLeaveDateBetweenAndYear(employee, fromDate, toDate, currentYear);

            leaveUsageLogRepository.deleteAll(logs);
        }
        else if ("Work From Home".equalsIgnoreCase(applicationType)) {
            // Delete WFH logs
            List<LeaveUsageLog> wfhLogs = leaveUsageLogRepository
                    .findByEmployeeAndLeaveTypeAndLeaveDateBetweenAndYear(
                            employee, "WFH", fromDate, toDate, currentYear
                    );

            leaveUsageLogRepository.deleteAll(wfhLogs);
        }
    }


    private void logAudit(VunnoResponse response, String actionType, String newStatus, String user, String role, String previousStatus, String description) {
        VunnoAuditLog log = new VunnoAuditLog();
        log.setVunnoResponseId(response.getId());
        log.setActionType(actionType);
        log.setPreviousStatus(previousStatus);
        log.setNewStatus(newStatus);
        log.setChangedBy(user);
        log.setChangedByRole(role);
        log.setChangedAt(LocalDateTime.now());
        log.setChangeReason(null); // If needed
        log.setChangeDescription(description);
        log.setPreviousValues(previousStatus);
        log.setNewValues(newStatus);

        vunnoAuditRepository.save(log);
    }
}