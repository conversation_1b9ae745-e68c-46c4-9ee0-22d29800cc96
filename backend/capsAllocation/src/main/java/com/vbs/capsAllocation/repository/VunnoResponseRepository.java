package com.vbs.capsAllocation.repository;

import com.vbs.capsAllocation.model.VunnoResponse;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface VunnoResponseRepository extends JpaRepository<VunnoResponse, Long> {

    @Query("SELECT v FROM VunnoResponse v WHERE v.employee.ldap = :ldap")
    List<VunnoResponse> findByEmployeeLdap(@Param("ldap") String ldap);

    List<VunnoResponse> findByEmployeeLdapAndStatusNot(String ldap, String status);

    @Query("SELECT v FROM VunnoResponse v WHERE v.employee.ldap IN :ldaps AND v.status = 'PENDING'")
    List<VunnoResponse> findPendingRequestsByTeamLdaps(@Param("ldaps") List<String> ldaps);

    @Query("SELECT v FROM VunnoResponse v WHERE v.employee.ldap IN :ldaps AND v.status = 'APPROVED'")
    List<VunnoResponse> findApprovedRequestsByTeamLdaps(@Param("ldaps") List<String> ldaps);

    List<VunnoResponse> findByEmployeeLdapInAndStatusIn(List<String> ldaps, List<String> statuses);

}