package com.vbs.capsAllocation.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // This maps /uploads/** to the physical directory on disk

        registry.addResourceHandler("/uploads/**")
                .addResourceLocations("file:/home/<USER>/teamsphere/backend/capsAllocation/uploads/");
    }
}
