package com.vbs.capsAllocation.util;

import org.springframework.stereotype.Component;

@Component
public class EmailTemplateUtil {



    public String getVunnoApprovalEmail(String backupInfo, String type, String approver, String fromDate, String toDate, String duration) {

        return "<html><body>"
                + "<h3>Your " + type + " request has been approved!</h3>"
                + "<p><b>Duration:</b> " + duration + "</p>"
                + "<p><b>From:</b> " + fromDate + " <b>To:</b> " + toDate + "</p>"
                + "<p><b>Approved By:</b> " + approver + "</p>"
                + "<p><b>Backup:</b> " + backupInfo + "</p>"
                + "<hr><p>Please coordinate with your backup in your absence.</p>"
                + "</body></html>";
    }


    public String getVunnoDeclineMail(){
        return "<!DOCTYPE html>\n"
                + "<html>\n"
                + "<head>\n"
                + "  <base target=\"_top\">\n"
                + "</head>\n"
                + "<body>\n"
                + "  <p><b>Your Request has been Declined.</b></p>\n"
                + "  <p>Please,feel free to contact your approver.</p>\n"
                + "</body>\n"
                + "</html>";
    }

}
