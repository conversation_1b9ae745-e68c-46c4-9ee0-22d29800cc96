package com.vbs.capsAllocation.service;

import org.springframework.stereotype.Service;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Date;

/**
 * Service for managing blacklisted JWT tokens
 * Tokens are added to the blacklist when a user logs out
 * or when a token is explicitly invalidated
 */
@Service
public class TokenBlacklistService {

    // Using ConcurrentHashMap for thread safety
    private final Map<String, Date> blacklistedTokens = new ConcurrentHashMap<>();

    /**
     * Add a token to the blacklist
     * @param token The JWT token to blacklist
     * @param expiryDate The expiry date of the token
     */
    public void blacklistToken(String token, Date expiryDate) {
        blacklistedTokens.put(token, expiryDate);
    }

    /**
     * Check if a token is blacklisted
     * @param token The JWT token to check
     * @return true if the token is blacklisted, false otherwise
     */
    public boolean isTokenBlacklisted(String token) {
        return blacklistedTokens.containsKey(token);
    }

    /**
     * Clean up expired tokens from the blacklist
     * This method should be called periodically to prevent memory leaks
     * @return The number of tokens removed
     */
    public int cleanupExpiredTokens() {
        Date now = new Date();
        int initialSize = blacklistedTokens.size();
        blacklistedTokens.entrySet().removeIf(entry -> entry.getValue().before(now));
        int finalSize = blacklistedTokens.size();
        return initialSize - finalSize;
    }

    /**
     * Get the current size of the blacklist
     * @return The number of tokens in the blacklist
     */
    public int getBlacklistSize() {
        return blacklistedTokens.size();
    }
}
