package com.vbs.capsAllocation.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class VunnoRequestDto {

    @JsonProperty("id")
    private Long id;

    @JsonProperty("ldap")
    private String ldap;

    @JsonProperty("approvingLead")
    private String approvingLead;

    @JsonProperty("applicationType")
    private String applicationType;

    @JsonProperty("leaveType")
    private String leaveType;

    @JsonProperty("lvWfhDuration")
    private String lvWfhDuration;

    @JsonProperty("startDate")
    private String startDate;

    @JsonProperty("endDate")
    private String endDate;

    private String startDateTime;
    private String endDateTime;

    @JsonProperty("backupInfo")
    private String backupInfo;

    @JsonProperty("oooProof")
    private String oooProof;

    @JsonProperty("timesheetProof")
    private String timesheetProof;

    @JsonProperty("status")
    private String status;

    @JsonProperty("requestorName")
    private String requestorName;

    @JsonProperty("reason")
    private String reason;

    @JsonProperty("document")
    private String documentPath;
}
