package com.vbs.capsAllocation.repository;

import com.vbs.capsAllocation.model.Employee;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface EmployeeRepository extends JpaRepository<Employee, Long> {

    @Query(value = "SELECT id, first_name, last_name, ldap, start_date, team, new_level, lead, program_manager, vendor, email, status, lwd_ml_start_date, process, resignation_date, role_change_effective_date, level_before_change, level_after_change, last_billing_date, backfill_ldap, billing_start_date, language, tenure_till_date, level, inactive_reason, NULL as profile_pic, parent, is_deleted, pnse_program, location, shift, inactive FROM Employee WHERE ldap = :ldap", nativeQuery = true)
    Optional<Employee> findByLdap(@Param("ldap") String ldap);

    boolean existsByLdap(String ldap);

    @Query(value = "SELECT id, first_name, last_name, ldap, start_date, team, new_level, lead, program_manager, vendor, email, status, lwd_ml_start_date, process, resignation_date, role_change_effective_date, level_before_change, level_after_change, last_billing_date, backfill_ldap, billing_start_date, language, tenure_till_date, level, inactive_reason, NULL as profile_pic, parent, is_deleted, pnse_program, location, shift, inactive FROM Employee WHERE lead = :leadLdap", nativeQuery = true)
    List<Employee> findByLead(@Param("leadLdap") String leadLdap);

    @Query(value = "SELECT id, first_name, last_name, ldap, start_date, team, new_level, lead, program_manager, vendor, email, status, lwd_ml_start_date, process, resignation_date, role_change_effective_date, level_before_change, level_after_change, last_billing_date, backfill_ldap, billing_start_date, language, tenure_till_date, level, inactive_reason, NULL as profile_pic, parent, is_deleted, pnse_program, location, shift, inactive FROM Employee WHERE program_manager = :programManagerLdap", nativeQuery = true)
    List<Employee> findByProgramManager(@Param("programManagerLdap") String programManagerLdap);

    // Native SQL queries to completely exclude LOB fields
    @Query(value = "SELECT id, first_name, last_name, ldap, start_date, team, new_level, lead, program_manager, vendor, email, status, lwd_ml_start_date, process, resignation_date, role_change_effective_date, level_before_change, level_after_change, last_billing_date, backfill_ldap, billing_start_date, language, tenure_till_date, level, inactive_reason, NULL as profile_pic, parent, is_deleted, pnse_program, location, shift, inactive FROM Employee", nativeQuery = true)
    List<Object[]> findAllExcludingLobsNative();

    @Query(value = "SELECT id, first_name, last_name, ldap, start_date, team, new_level, lead, program_manager, vendor, email, status, lwd_ml_start_date, process, resignation_date, role_change_effective_date, level_before_change, level_after_change, last_billing_date, backfill_ldap, billing_start_date, language, tenure_till_date, level, inactive_reason, NULL as profile_pic, parent, is_deleted, pnse_program, location, shift, inactive FROM Employee WHERE lead = :leadLdap", nativeQuery = true)
    List<Object[]> findByLeadExcludingLobsNative(@Param("leadLdap") String leadLdap);

    @Query(value = "SELECT id, first_name, last_name, ldap, start_date, team, new_level, lead, program_manager, vendor, email, status, lwd_ml_start_date, process, resignation_date, role_change_effective_date, level_before_change, level_after_change, last_billing_date, backfill_ldap, billing_start_date, language, tenure_till_date, level, inactive_reason, NULL as profile_pic, parent, is_deleted, pnse_program, location, shift, inactive FROM Employee WHERE id = :id", nativeQuery = true)
    Employee findByIdExcludingLobsNative(@Param("id") Long id);

    @Query(value = "SELECT id, first_name, last_name, ldap, start_date, team, new_level, lead, program_manager, vendor, email, status, lwd_ml_start_date, process, resignation_date, role_change_effective_date, level_before_change, level_after_change, last_billing_date, backfill_ldap, billing_start_date, language, tenure_till_date, level, inactive_reason, NULL as profile_pic, parent, is_deleted, pnse_program, location, shift, inactive FROM Employee WHERE id IN :userIds", nativeQuery = true)
    List<Object[]> findAllByIdExcludingLobsNative(@Param("userIds") List<Long> userIds);

    @Query("SELECT e.ldap FROM Employee e WHERE e.lead = :leadLdap")
    List<String> findLdapsByLead(@Param("leadLdap") String leadLdap);

    @Query("SELECT e.ldap FROM Employee e WHERE e.programManager = :managerLdap")
    List<String> findLdapsByManager(@Param("managerLdap") String managerLdap);

    @Query("SELECT e.ldap FROM Employee e")
    List<String> findAllLdaps();


}