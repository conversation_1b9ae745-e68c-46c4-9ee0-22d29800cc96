package com.vbs.capsAllocation.config;

import com.vbs.capsAllocation.util.JwtFilter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.csrf.CookieCsrfTokenRepository;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.List;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

    private final JwtFilter jwtFilter;

    public SecurityConfig(JwtFilter jwtFilter) {
        this.jwtFilter = jwtFilter;
    }

    @Bean
    public AuthenticationManager authenticationManager(HttpSecurity http, PasswordEncoder encoder, UserDetailsService userDetailsService) throws Exception {
        AuthenticationManagerBuilder builder = http.getSharedObject(AuthenticationManagerBuilder.class);
        builder.userDetailsService(userDetailsService).passwordEncoder(encoder);
        return builder.build();
    }

    @SuppressWarnings({ "deprecation", "removal" })
    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
                .cors(cors -> cors.configurationSource(corsConfigurationSource()))
                // Enable CSRF protection but exclude authentication and password reset endpoints
                .csrf(csrf -> csrf
                    .ignoringRequestMatchers(
                        "/auth/login",
			"/auth/signup",
                        "/auth/logout",
                        "/auth/check-password-status",
                        "/auth/reset-password",
                        "/auth/forgot-password",
                        "/auth/verify-otp",
                        "/auth/reset-password-with-otp",
                        "/admin/register",
                        "/admin/reset-password-postman",
                        "/api/time-entries/**",
                        "/api/timesheets/**",
                        "/api/projects/**",
                        "/api/employees/**",
                        "/api/database/**"
                    )
                    .csrfTokenRepository(CookieCsrfTokenRepository.withHttpOnlyFalse())
                )
                .authorizeRequests()
                .requestMatchers("/auth/login").permitAll()
                .requestMatchers("/auth/forgot-password").permitAll()
                .requestMatchers("/auth/verify-otp").permitAll()
                .requestMatchers("/auth/reset-password-with-otp").permitAll()
		.requestMatchers("/auth/signup").permitAll()
                .requestMatchers("/admin/register").permitAll()
                .requestMatchers("/auth/check-password-status").authenticated()
                .requestMatchers("/auth/reset-password").authenticated()
                .requestMatchers("api/vunno/currentUserLdap/**").authenticated()
                .requestMatchers("/superadmin/only").hasRole("ACCOUNT_MANAGER")
                .requestMatchers("/admin/only").hasRole("MANAGER")
                .requestMatchers("/user/only").hasAnyRole("USER", "MANAGER", "ACCOUNT_MANAGER")
                .requestMatchers("/api/employees/**","/api/timesheet/**").hasAnyRole("USER","LEAD","MANAGER", "ACCOUNT_MANAGER", "ADMIN_OPS_MANAGER")
                // Secure admin endpoints with proper role checks
                .requestMatchers("/admin/reset-password-postman/**").hasAnyRole("ADMIN_OPS_MANAGER", "LEAD", "MANAGER")
                .requestMatchers("/api/employees/change-role").hasRole("ADMIN_OPS_MANAGER")
                .requestMatchers("/api/database/**").hasRole("ADMIN_OPS_MANAGER")
                .anyRequest().authenticated()
                .and()
                .addFilterBefore(jwtFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();

        // Comprehensive CORS origins for Google Compute Engine deployment
        List<String> allowedOrigins = new ArrayList<>(Arrays.asList(
            "http://localhost",         // Production frontend (port 80)
            "http://localhost:4200",    // Angular dev server
            "http://localhost:8080",    // Backend default port
            "http://localhost:8081",    // Backend test port
            "http://localhost:8082",    // Backend test port 2
            "http://localhost:3000",    // React dev server (if used)
            "http://localhost:80",      // Production frontend explicit
            "http://127.0.0.1",         // Alternative localhost
            "http://127.0.0.1:4200",    // Alternative localhost
            "http://127.0.0.1:8080",
            "http://127.0.0.1:8081",
            "http://127.0.0.1:8082",
            // Google Compute Engine domain-based origins
            "https://teamsphere.in",    // Production frontend domain (HTTPS)
            "http://teamsphere.in",     // Production frontend domain (HTTP)
            "https://www.teamsphere.in", // Production frontend domain with www (HTTPS)
            "http://www.teamsphere.in",  // Production frontend domain with www (HTTP)
            "https://api.teamsphere.in", // Backend API domain (HTTPS)
            "http://api.teamsphere.in"   // Backend API domain (HTTP)
        ));

        // Add dynamic IP addresses for Chromebook/WSL environments
        try {
            // Get all network interfaces
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            while (interfaces.hasMoreElements()) {
                NetworkInterface networkInterface = interfaces.nextElement();
                if (!networkInterface.isLoopback() && networkInterface.isUp()) {
                    Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                    while (addresses.hasMoreElements()) {
                        InetAddress address = addresses.nextElement();
                        if (address instanceof Inet4Address && !address.isLoopbackAddress()) {
                            String ip = address.getHostAddress();
                            // Add various port combinations for the detected IP
                            allowedOrigins.add("http://" + ip);
                            allowedOrigins.add("http://" + ip + ":80");
                            allowedOrigins.add("http://" + ip + ":4200");
                            allowedOrigins.add("http://" + ip + ":8080");
                            allowedOrigins.add("http://" + ip + ":8081");
                            allowedOrigins.add("http://" + ip + ":8082");
                            allowedOrigins.add("https://" + ip);
                            allowedOrigins.add("https://" + ip + ":80");
                            allowedOrigins.add("https://" + ip + ":4200");
                            allowedOrigins.add("https://" + ip + ":8080");
                            allowedOrigins.add("https://" + ip + ":8081");
                            allowedOrigins.add("https://" + ip + ":8082");
                        }
                    }
                }
            }
        } catch (Exception e) {
            // Log the error but continue with default origins
            System.err.println("Warning: Could not detect network interfaces for CORS configuration: " + e.getMessage());
        }

        // Log all allowed origins for debugging
        System.out.println("CORS Configuration - Allowed Origins:");
        for (String origin : allowedOrigins) {
            System.out.println("  - " + origin);
        }

        configuration.setAllowedOrigins(allowedOrigins);
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);
        configuration.setExposedHeaders(Arrays.asList("Authorization", "X-XSRF-TOKEN"));
        configuration.setMaxAge(3600L);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
