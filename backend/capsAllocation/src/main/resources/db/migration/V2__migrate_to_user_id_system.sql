-- Migration script to transform LDAP-based system to User ID-based system
-- This script adds new tables and columns while maintaining backward compatibility

-- Create clients table for multi-tenant support
CREATE TABLE IF NOT EXISTS clients (
    id BIGSERIAL PRIMARY KEY,
    client_code VARCHAR(50) UNIQUE NOT NULL,
    client_name VARCHAR(255) NOT NULL,
    email_domain VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create field_configurations table for flexible field management
CREATE TABLE IF NOT EXISTS field_configurations (
    id BIGSERIAL PRIMARY KEY,
    client_id BIGINT NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    field_name VARCHAR(100) NOT NULL,
    field_label VARCHAR(255) NOT NULL,
    field_type VARCHAR(50) NOT NULL,
    is_required BOOLEAN DEFAULT false,
    is_visible BOOLEAN DEFAULT true,
    field_order INTEGER DEFAULT 0,
    validation_rules TEXT,
    default_value VARCHAR(500),
    options TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(client_id, field_name)
);

-- Insert default client for existing data
INSERT INTO clients (client_code, client_name, email_domain, is_active) 
VALUES ('DEFAULT', 'Default Client', 'google.com', true)
ON CONFLICT (client_code) DO NOTHING;

-- Add new columns to Users table
ALTER TABLE "Users" ADD COLUMN IF NOT EXISTS client_id BIGINT;
ALTER TABLE "Users" ADD COLUMN IF NOT EXISTS user_id VARCHAR(100);
ALTER TABLE "Users" ADD COLUMN IF NOT EXISTS email VARCHAR(255);
ALTER TABLE "Users" ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;
ALTER TABLE "Users" ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE "Users" ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Set default client for existing users
UPDATE "Users" SET client_id = (SELECT id FROM clients WHERE client_code = 'DEFAULT') WHERE client_id IS NULL;

-- Populate user_id and email from username (assuming username is email-like)
UPDATE "Users" SET 
    user_id = CASE 
        WHEN username LIKE '%@%' THEN SPLIT_PART(username, '@', 1)
        ELSE username
    END,
    email = CASE 
        WHEN username LIKE '%@%' THEN username
        ELSE username || '@google.com'
    END
WHERE user_id IS NULL OR email IS NULL;

-- Add constraints after data population
ALTER TABLE "Users" ALTER COLUMN client_id SET NOT NULL;
ALTER TABLE "Users" ADD CONSTRAINT fk_users_client FOREIGN KEY (client_id) REFERENCES clients(id);

-- Create unique constraints for Users table
CREATE UNIQUE INDEX IF NOT EXISTS idx_users_user_id ON "Users"(user_id);
CREATE UNIQUE INDEX IF NOT EXISTS idx_users_email ON "Users"(email);

-- Add new columns to Employee table
ALTER TABLE "Employee" ADD COLUMN IF NOT EXISTS client_id BIGINT;
ALTER TABLE "Employee" ADD COLUMN IF NOT EXISTS user_id VARCHAR(100);
ALTER TABLE "Employee" ADD COLUMN IF NOT EXISTS lead_user_id VARCHAR(100);
ALTER TABLE "Employee" ADD COLUMN IF NOT EXISTS program_manager_user_id VARCHAR(100);
ALTER TABLE "Employee" ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE "Employee" ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Set default client for existing employees
UPDATE "Employee" SET client_id = (SELECT id FROM clients WHERE client_code = 'DEFAULT') WHERE client_id IS NULL;

-- Populate user_id from email or ldap
UPDATE "Employee" SET 
    user_id = CASE 
        WHEN email IS NOT NULL AND email LIKE '%@%' THEN SPLIT_PART(email, '@', 1)
        WHEN ldap IS NOT NULL THEN ldap
        ELSE 'unknown_' || id::text
    END
WHERE user_id IS NULL;

-- Populate lead_user_id and program_manager_user_id from existing lead and programManager fields
UPDATE "Employee" SET 
    lead_user_id = lead,
    program_manager_user_id = "programManager"
WHERE lead_user_id IS NULL OR program_manager_user_id IS NULL;

-- Add constraints after data population
ALTER TABLE "Employee" ALTER COLUMN client_id SET NOT NULL;
ALTER TABLE "Employee" ADD CONSTRAINT fk_employee_client FOREIGN KEY (client_id) REFERENCES clients(id);

-- Create unique constraint for Employee user_id
CREATE UNIQUE INDEX IF NOT EXISTS idx_employee_user_id ON "Employee"(user_id);

-- Add new columns to time_entries table
ALTER TABLE time_entries ADD COLUMN IF NOT EXISTS client_id BIGINT;
ALTER TABLE time_entries ADD COLUMN IF NOT EXISTS user_id_field VARCHAR(100);
ALTER TABLE time_entries ADD COLUMN IF NOT EXISTS reporting_senior_user_id VARCHAR(100);
ALTER TABLE time_entries ADD COLUMN IF NOT EXISTS resource_name VARCHAR(255);
ALTER TABLE time_entries ADD COLUMN IF NOT EXISTS company VARCHAR(255);
ALTER TABLE time_entries ADD COLUMN IF NOT EXISTS entry_type VARCHAR(100);
ALTER TABLE time_entries ADD COLUMN IF NOT EXISTS notes TEXT;
ALTER TABLE time_entries ADD COLUMN IF NOT EXISTS internal_notes TEXT;
ALTER TABLE time_entries ADD COLUMN IF NOT EXISTS date_worked DATE;
ALTER TABLE time_entries ADD COLUMN IF NOT EXISTS hours_worked DECIMAL(5,2);
ALTER TABLE time_entries ADD COLUMN IF NOT EXISTS billable_hours_worked DECIMAL(5,2);
ALTER TABLE time_entries ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE time_entries ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Set default client for existing time entries
UPDATE time_entries SET client_id = (SELECT id FROM clients WHERE client_code = 'DEFAULT') WHERE client_id IS NULL;

-- Populate new fields from existing data
UPDATE time_entries SET 
    user_id_field = ldap,
    date_worked = entry_date,
    hours_worked = ROUND(time_in_mins::decimal / 60, 2),
    notes = comment,
    resource_name = (
        SELECT COALESCE(last_name, '') || CASE WHEN last_name IS NOT NULL AND first_name IS NOT NULL THEN ', ' ELSE '' END || COALESCE(first_name, '')
        FROM "Employee" e 
        WHERE e.ldap = time_entries.ldap OR e.user_id = time_entries.ldap
        LIMIT 1
    ),
    company = 'ABC' -- Default company as per requirements
WHERE user_id_field IS NULL OR date_worked IS NULL OR hours_worked IS NULL;

-- Add constraints after data population
ALTER TABLE time_entries ALTER COLUMN client_id SET NOT NULL;
ALTER TABLE time_entries ADD CONSTRAINT fk_time_entries_client FOREIGN KEY (client_id) REFERENCES clients(id);

-- Create table for custom fields storage
CREATE TABLE IF NOT EXISTS time_entry_custom_fields (
    time_entry_id BIGINT NOT NULL REFERENCES time_entries(id) ON DELETE CASCADE,
    field_name VARCHAR(100) NOT NULL,
    field_value TEXT,
    PRIMARY KEY (time_entry_id, field_name)
);

-- Insert default field configurations for the default client
INSERT INTO field_configurations (client_id, field_name, field_label, field_type, is_required, is_visible, field_order) 
SELECT 
    (SELECT id FROM clients WHERE client_code = 'DEFAULT'),
    field_name,
    field_label,
    field_type,
    is_required,
    is_visible,
    field_order
FROM (VALUES
    ('userIdField', 'User ID', 'TEXT', true, true, 1),
    ('reportingSeniorUserId', 'Reporting Senior''s UserID', 'TEXT', false, true, 2),
    ('resourceName', 'Resource Name', 'TEXT', true, true, 3),
    ('company', 'Company', 'TEXT', false, true, 4),
    ('entryType', 'Type', 'SELECT', false, true, 5),
    ('notes', 'Notes', 'TEXTAREA', false, true, 6),
    ('internalNotes', 'Internal Notes', 'TEXTAREA', false, true, 7),
    ('dateWorked', 'Date Worked', 'DATE', true, true, 8),
    ('hoursWorked', 'Hours Worked', 'NUMBER', true, true, 9),
    ('billableHoursWorked', 'Billable Hours Worked', 'NUMBER', false, true, 10)
) AS default_fields(field_name, field_label, field_type, is_required, is_visible, field_order)
ON CONFLICT (client_id, field_name) DO NOTHING;

-- Update field configuration for entryType with options
UPDATE field_configurations 
SET options = '["Ticket", "Project", "Meeting", "Training", "Support", "Other"]'
WHERE field_name = 'entryType' AND client_id = (SELECT id FROM clients WHERE client_code = 'DEFAULT');

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_time_entries_user_id_field ON time_entries(user_id_field);
CREATE INDEX IF NOT EXISTS idx_time_entries_date_worked ON time_entries(date_worked);
CREATE INDEX IF NOT EXISTS idx_time_entries_client_id ON time_entries(client_id);
CREATE INDEX IF NOT EXISTS idx_employee_client_id ON "Employee"(client_id);
CREATE INDEX IF NOT EXISTS idx_users_client_id ON "Users"(client_id);

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to tables
DROP TRIGGER IF EXISTS update_clients_updated_at ON clients;
CREATE TRIGGER update_clients_updated_at BEFORE UPDATE ON clients FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_field_configurations_updated_at ON field_configurations;
CREATE TRIGGER update_field_configurations_updated_at BEFORE UPDATE ON field_configurations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_users_updated_at ON "Users";
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON "Users" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_employee_updated_at ON "Employee";
CREATE TRIGGER update_employee_updated_at BEFORE UPDATE ON "Employee" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_time_entries_updated_at ON time_entries;
CREATE TRIGGER update_time_entries_updated_at BEFORE UPDATE ON time_entries FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
