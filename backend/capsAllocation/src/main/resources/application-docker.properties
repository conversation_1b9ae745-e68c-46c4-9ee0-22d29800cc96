# Docker-specific configuration for TeamSphere Backend
# This file is used when SPRING_PROFILES_ACTIVE includes 'docker'

# Database Configuration - Docker
spring.datasource.url=***************************************************
spring.datasource.username=postgres
spring.datasource.password=voyage
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.schema_validation.enabled=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.default_schema=public
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.open-in-view=false

# LOB handling configuration for PostgreSQL
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false

# Server Configuration
server.port=8080
server.address=0.0.0.0

# File Upload Configuration
spring.web.resources.static-locations=file:/app/uploads/profile-pics
spring.servlet.multipart.max-file-size=1MB
spring.servlet.multipart.max-request-size=1MB

# Google Sheets Configuration
google.sheets.spreadsheetId=${GOOGLE_SHEETS_SPREADSHEET_ID:14USVFIoRJQzSgKA9_DI3_c_rOJy7G2W9txJRNRDCfAw}
google.sheets.credentials=file:/app/credentials/ops-excellence-a969197613f8.json
google.sheets.sheetName=${GOOGLE_SHEETS_SHEET_NAME:Response}

# Logging Configuration
logging.level.root=INFO
logging.level.org.springframework=INFO
logging.level.com.vbs.capsAllocation=DEBUG
logging.file.name=/app/logs/teamsphere.log
logging.file.max-size=10MB
logging.file.max-history=30
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# JavaMail Configuration (legacy - for backward compatibility)
spring.mail.host=localhost
spring.mail.port=25
spring.mail.username=<EMAIL>
spring.mail.password=dummy-password
spring.mail.properties.mail.smtp.auth=false
spring.mail.properties.mail.smtp.starttls.enable=false

# Amazon SES Configuration
aws.ses.access-key=${AWS_SES_ACCESS_KEY:********************}
aws.ses.secret-key=${AWS_SES_SECRET_KEY:ag2ezW/wESE5X0BFLrCUUf+evzfpGUqMTavvJRRS}
aws.ses.region=${AWS_SES_REGION:us-east-1}
aws.ses.from-email=${AWS_SES_FROM_EMAIL:<EMAIL>}
aws.ses.endpoint-override=https://email.us-east-1.amazonaws.com

# Backup Configuration
backup.share.email=${BACKUP_SHARE_EMAIL:<EMAIL>}

# Management Endpoints
management.endpoints.web.exposure.include=health,info,metrics,env
management.endpoint.health.show-details=when-authorized
management.health.db.enabled=true
management.info.env.enabled=true

# Security Configuration
spring.security.require-ssl=false

# Connection Pool Configuration
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.max-lifetime=600000
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.leak-detection-threshold=60000

# Cache Configuration
spring.cache.type=simple

# Scheduling Configuration
spring.task.scheduling.pool.size=5

# Docker-specific optimizations
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true
