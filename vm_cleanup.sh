#!/bin/bash

# VM Cleanup Script - Free up disk space on teamsphere-staging VM
# This script can be run locally or remotely on the VM

set -e

# === CONFIGURATION ===
VM_NAME="teamsphere-staging"
ZONE="asia-south2-c"
RUN_LOCALLY=false

# === Function to display disk usage ===
show_disk_usage() {
    echo "=== CURRENT DISK USAGE ==="
    df -h
    echo ""
    echo "=== LARGEST DIRECTORIES ==="
    du -h --max-depth=1 / 2>/dev/null | sort -hr | head -10
    echo ""
}

# === Function to clean package cache ===
clean_package_cache() {
    echo "🧹 Cleaning package cache..."
    sudo apt-get clean
    sudo apt-get autoclean
    sudo apt-get autoremove -y
    echo "✅ Package cache cleaned"
}

# === Function to clean logs ===
clean_logs() {
    echo "🗂️ Cleaning system logs..."
    
    # Clean journal logs older than 7 days
    sudo journalctl --vacuum-time=7d
    
    # Clean old log files
    sudo find /var/log -type f -name "*.log" -mtime +7 -delete 2>/dev/null || true
    sudo find /var/log -type f -name "*.log.*" -delete 2>/dev/null || true
    
    # Clean syslog files
    sudo find /var/log -name "syslog.*" -delete 2>/dev/null || true
    
    echo "✅ System logs cleaned"
}

# === Function to clean temporary files ===
clean_temp_files() {
    echo "🗑️ Cleaning temporary files..."
    
    # Clean /tmp (keep files newer than 1 day)
    sudo find /tmp -type f -atime +1 -delete 2>/dev/null || true
    
    # Clean user temp files
    find ~/.cache -type f -atime +7 -delete 2>/dev/null || true
    
    # Clean thumbnails
    rm -rf ~/.thumbnails/* 2>/dev/null || true
    
    echo "✅ Temporary files cleaned"
}

# === Function to clean old kernels ===
clean_old_kernels() {
    echo "🔧 Cleaning old kernel versions..."
    
    # Keep current and one previous kernel
    CURRENT_KERNEL=$(uname -r)
    echo "Current kernel: $CURRENT_KERNEL"
    
    # List installed kernels and remove old ones (keep current + 1 previous)
    dpkg -l | grep -E "linux-image-[0-9]" | grep -v "$CURRENT_KERNEL" | awk '{print $2}' | sort -V | head -n -1 | xargs -r sudo apt-get purge -y 2>/dev/null || true
    
    echo "✅ Old kernels cleaned"
}

# === Function to clean Docker (if installed) ===
clean_docker() {
    if command -v docker &> /dev/null; then
        echo "🐳 Cleaning Docker resources..."
        
        # Remove unused containers, networks, images
        sudo docker system prune -af 2>/dev/null || true
        
        # Remove unused volumes
        sudo docker volume prune -f 2>/dev/null || true
        
        echo "✅ Docker resources cleaned"
    else
        echo "ℹ️ Docker not installed, skipping Docker cleanup"
    fi
}

# === Function to clean snap packages ===
clean_snap() {
    if command -v snap &> /dev/null; then
        echo "📦 Cleaning snap packages..."
        
        # Remove old snap revisions (keep only 2 most recent)
        snap list --all | awk '/disabled/{print $1, $3}' | while read snapname revision; do
            sudo snap remove "$snapname" --revision="$revision" 2>/dev/null || true
        done
        
        echo "✅ Snap packages cleaned"
    else
        echo "ℹ️ Snap not installed, skipping snap cleanup"
    fi
}

# === Function to find and optionally remove large files ===
find_large_files() {
    echo "🔍 Finding large files (>100MB)..."
    echo "=== FILES LARGER THAN 100MB ==="
    find / -type f -size +100M -exec ls -lh {} \; 2>/dev/null | head -20
    echo ""
    echo "💡 Review these files and manually delete if not needed"
}

# === Main cleanup function ===
run_cleanup() {
    echo "🚀 Starting VM cleanup process..."
    echo ""
    
    # Show initial disk usage
    show_disk_usage
    
    # Run cleanup operations
    clean_package_cache
    clean_logs
    clean_temp_files
    clean_old_kernels
    clean_docker
    clean_snap
    
    echo ""
    echo "=== CLEANUP COMPLETED ==="
    echo ""
    
    # Show final disk usage
    show_disk_usage
    
    # Find large files for manual review
    find_large_files
    
    echo ""
    echo "✅ VM cleanup completed successfully!"
}

# === Remote execution function ===
run_remote_cleanup() {
    echo "🌐 Running cleanup on remote VM: $VM_NAME"
    
    # Copy this script to the VM and execute it
    gcloud compute scp "$0" "$VM_NAME:~/vm_cleanup.sh" --zone "$ZONE"
    gcloud compute ssh "$VM_NAME" --zone "$ZONE" --command "chmod +x ~/vm_cleanup.sh && ~/vm_cleanup.sh --local"
}

# === Main execution ===
case "${1:-}" in
    --local)
        RUN_LOCALLY=true
        run_cleanup
        ;;
    --remote)
        run_remote_cleanup
        ;;
    *)
        echo "VM Cleanup Script"
        echo ""
        echo "Usage:"
        echo "  $0 --local    Run cleanup locally on current machine"
        echo "  $0 --remote   Run cleanup on remote VM ($VM_NAME)"
        echo ""
        echo "Current disk usage:"
        df -h 2>/dev/null || echo "Run with --local or --remote to see disk usage"
        ;;
esac
