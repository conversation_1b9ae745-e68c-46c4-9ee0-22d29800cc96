#!/bin/bash

# Script to convert PostgreSQL custom format dump to different formats
# Usage: ./create_different_dump_formats.sh [input_dump_file] [output_prefix]

INPUT_DUMP=${1:-"db1.dump"}
OUTPUT_PREFIX=${2:-"db1"}

echo "=== PostgreSQL Dump Format Converter ==="
echo "Input file: $INPUT_DUMP"
echo "Output prefix: $OUTPUT_PREFIX"
echo ""

# Check if input dump file exists
if [ ! -f "$INPUT_DUMP" ]; then
    echo "Error: Input dump file '$INPUT_DUMP' not found!"
    exit 1
fi

echo "Converting to different dump formats..."
echo ""

# 1. Plain SQL format (most compatible)
echo "1. Creating plain SQL dump..."
pg_restore --verbose --no-acl --no-owner -f "${OUTPUT_PREFIX}_plain.sql" "$INPUT_DUMP"
if [ $? -eq 0 ]; then
    echo "✅ Created: ${OUTPUT_PREFIX}_plain.sql"
else
    echo "❌ Failed to create plain SQL dump"
fi
echo ""

# 2. Schema-only SQL dump
echo "2. Creating schema-only SQL dump..."
pg_restore --verbose --no-acl --no-owner --schema-only -f "${OUTPUT_PREFIX}_schema_only.sql" "$INPUT_DUMP"
if [ $? -eq 0 ]; then
    echo "✅ Created: ${OUTPUT_PREFIX}_schema_only.sql"
else
    echo "❌ Failed to create schema-only dump"
fi
echo ""

# 3. Data-only SQL dump
echo "3. Creating data-only SQL dump..."
pg_restore --verbose --no-acl --no-owner --data-only -f "${OUTPUT_PREFIX}_data_only.sql" "$INPUT_DUMP"
if [ $? -eq 0 ]; then
    echo "✅ Created: ${OUTPUT_PREFIX}_data_only.sql"
else
    echo "❌ Failed to create data-only dump"
fi
echo ""

# 4. Compressed SQL dump (gzip)
echo "4. Creating compressed SQL dump..."
pg_restore --verbose --no-acl --no-owner "$INPUT_DUMP" | gzip > "${OUTPUT_PREFIX}_compressed.sql.gz"
if [ $? -eq 0 ]; then
    echo "✅ Created: ${OUTPUT_PREFIX}_compressed.sql.gz"
else
    echo "❌ Failed to create compressed dump"
fi
echo ""

# 5. Directory format dump (requires temporary database)
echo "5. Creating directory format dump..."
TEMP_DB="temp_dump_conversion_$$"
echo "   Creating temporary database: $TEMP_DB"
createdb "$TEMP_DB" 2>/dev/null

if [ $? -eq 0 ]; then
    echo "   Restoring to temporary database..."
    pg_restore --verbose --no-acl --no-owner -d "$TEMP_DB" "$INPUT_DUMP" >/dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        echo "   Creating directory format dump..."
        pg_dump --verbose --no-acl --no-owner --format=directory --file="${OUTPUT_PREFIX}_directory" "$TEMP_DB" >/dev/null 2>&1
        
        if [ $? -eq 0 ]; then
            echo "✅ Created: ${OUTPUT_PREFIX}_directory/ (directory format)"
        else
            echo "❌ Failed to create directory format dump"
        fi
    else
        echo "❌ Failed to restore to temporary database"
    fi
    
    echo "   Cleaning up temporary database..."
    dropdb "$TEMP_DB" 2>/dev/null
else
    echo "❌ Failed to create temporary database"
fi
echo ""

# 6. TAR format dump (requires temporary database)
echo "6. Creating TAR format dump..."
TEMP_DB2="temp_dump_conversion2_$$"
echo "   Creating temporary database: $TEMP_DB2"
createdb "$TEMP_DB2" 2>/dev/null

if [ $? -eq 0 ]; then
    echo "   Restoring to temporary database..."
    pg_restore --verbose --no-acl --no-owner -d "$TEMP_DB2" "$INPUT_DUMP" >/dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        echo "   Creating TAR format dump..."
        pg_dump --verbose --no-acl --no-owner --format=tar --file="${OUTPUT_PREFIX}_tar.tar" "$TEMP_DB2" >/dev/null 2>&1
        
        if [ $? -eq 0 ]; then
            echo "✅ Created: ${OUTPUT_PREFIX}_tar.tar"
        else
            echo "❌ Failed to create TAR format dump"
        fi
    else
        echo "❌ Failed to restore to temporary database"
    fi
    
    echo "   Cleaning up temporary database..."
    dropdb "$TEMP_DB2" 2>/dev/null
else
    echo "❌ Failed to create temporary database"
fi
echo ""

# Show file sizes and information
echo "=== Created Files Summary ==="
echo ""
for file in "${OUTPUT_PREFIX}_plain.sql" "${OUTPUT_PREFIX}_schema_only.sql" "${OUTPUT_PREFIX}_data_only.sql" "${OUTPUT_PREFIX}_compressed.sql.gz" "${OUTPUT_PREFIX}_tar.tar"; do
    if [ -f "$file" ]; then
        size=$(du -h "$file" | cut -f1)
        echo "📄 $file ($size)"
    fi
done

if [ -d "${OUTPUT_PREFIX}_directory" ]; then
    size=$(du -sh "${OUTPUT_PREFIX}_directory" | cut -f1)
    echo "📁 ${OUTPUT_PREFIX}_directory/ ($size)"
fi

echo ""
echo "=== Usage Instructions ==="
echo ""
echo "Plain SQL format (most compatible):"
echo "  psql -d target_database -f ${OUTPUT_PREFIX}_plain.sql"
echo ""
echo "Compressed SQL format:"
echo "  gunzip -c ${OUTPUT_PREFIX}_compressed.sql.gz | psql -d target_database"
echo ""
echo "TAR format:"
echo "  pg_restore -d target_database ${OUTPUT_PREFIX}_tar.tar"
echo ""
echo "Directory format:"
echo "  pg_restore -d target_database ${OUTPUT_PREFIX}_directory/"
echo ""
echo "Schema only + Data only (separate):"
echo "  psql -d target_database -f ${OUTPUT_PREFIX}_schema_only.sql"
echo "  psql -d target_database -f ${OUTPUT_PREFIX}_data_only.sql"
