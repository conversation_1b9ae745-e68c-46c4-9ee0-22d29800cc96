#!/bin/bash

# Script to properly restore PostgreSQL custom format dump with data
# Usage: ./restore_dump_properly.sh [database_name] [dump_file]

DATABASE_NAME=${1:-"new_database_name"}
DUMP_FILE=${2:-"db1.dump"}

echo "=== PostgreSQL Custom Format Dump Restoration ==="
echo "Database: $DATABASE_NAME"
echo "Dump file: $DUMP_FILE"
echo ""

# Check if dump file exists
if [ ! -f "$DUMP_FILE" ]; then
    echo "Error: Dump file '$DUMP_FILE' not found!"
    exit 1
fi

# Method 1: Create database and restore using pg_restore
echo "1. Creating database '$DATABASE_NAME'..."
createdb "$DATABASE_NAME" 2>/dev/null || echo "Database might already exist, continuing..."

echo "2. Restoring data using pg_restore..."
pg_restore --verbose --clean --no-acl --no-owner -d "$DATABASE_NAME" "$DUMP_FILE"

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Restoration completed successfully!"
    echo ""
    echo "To verify data was imported, run:"
    echo "psql -d $DATABASE_NAME -c \"SELECT schemaname, tablename, n_tup_ins as rows FROM pg_stat_user_tables WHERE n_tup_ins > 0;\""
else
    echo ""
    echo "❌ Restoration failed. Trying alternative method..."
    echo ""
    
    # Method 2: Alternative restoration with different options
    echo "3. Trying alternative restoration method..."
    pg_restore --verbose --clean --if-exists --no-acl --no-owner --disable-triggers -d "$DATABASE_NAME" "$DUMP_FILE"
    
    if [ $? -eq 0 ]; then
        echo "✅ Alternative restoration completed!"
    else
        echo "❌ Both restoration methods failed."
        echo ""
        echo "Possible solutions:"
        echo "1. Check PostgreSQL version compatibility"
        echo "2. Verify dump file integrity: pg_restore --list $DUMP_FILE"
        echo "3. Try restoring to a different database"
        echo "4. Check PostgreSQL logs for detailed error messages"
    fi
fi

echo ""
echo "=== Verification Commands ==="
echo "# Connect to database:"
echo "psql -d $DATABASE_NAME"
echo ""
echo "# Check tables with data:"
echo "psql -d $DATABASE_NAME -c \"\\dt\""
echo "psql -d $DATABASE_NAME -c \"SELECT schemaname, tablename, n_tup_ins as rows FROM pg_stat_user_tables;\""
echo ""
echo "# Check specific table row counts:"
echo "psql -d $DATABASE_NAME -c \"SELECT 'employee' as table_name, count(*) as row_count FROM employee UNION ALL SELECT 'users', count(*) FROM users;\""
