#!/bin/bash

# === Configurable Variables ===
INPUT_FILE="$1"  # Path to your original .sql file
DB_NAME="vbs_allocation_caps"  # Target PostgreSQL database
OUTPUT_FILE="cleaned_${INPUT_FILE##*/}"  # Output filename prefixed with 'cleaned_'

# === Validate input ===
if [ ! -f "$INPUT_FILE" ]; then
  echo "❌ Error: Input file not found: $INPUT_FILE"
  exit 1
fi

echo "📄 Cleaning file: $INPUT_FILE"
echo "🧼 Preserving LOBs and adding DROP TABLE statements..."

# Extract all table names and prepend DROP statements
TABLE_NAMES=$(grep -oP 'CREATE TABLE public\.\K\w+' "$INPUT_FILE")
DROP_STATEMENTS=""
for table in $TABLE_NAMES; do
  DROP_STATEMENTS+="DROP TABLE IF EXISTS public.$table CASCADE;\n"
done

# Keep LOBs, just prepend DROP statements
{
  echo -e "$DROP_STATEMENTS"
  cat "$INPUT_FILE"
} > "$OUTPUT_FILE"

echo "✅ Cleaned file saved to: $OUTPUT_FILE"

# === Remove existing LOBs from target DB ===
echo "🧹 Removing existing Large Objects from DB: $DB_NAME"
sudo -u postgres psql -d "$DB_NAME" -c "
DO \$\$
DECLARE
  r oid;
BEGIN
  FOR r IN SELECT oid FROM pg_largeobject_metadata LOOP
    PERFORM lo_unlink(r);
  END LOOP;
END
\$\$;
"

# === Restore cleaned SQL ===
echo "📦 Importing cleaned SQL into $DB_NAME..."
sudo -u postgres psql -d "$DB_NAME" -f "$OUTPUT_FILE"

echo "✅ Done. SQL file imported with LOBs preserved."

