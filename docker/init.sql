-- TeamSphere Database Initialization Script
-- This script runs when the PostgreSQL container starts for the first time

-- Create database if it doesn't exist (handled by POSTGRES_DB environment variable)
-- CREATE DATABASE IF NOT EXISTS vbs_allocation_caps;

-- Connect to the database
\c vbs_allocation_caps;

-- Create schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS public;

-- <PERSON> permissions
GRANT ALL PRIVILEGES ON SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres;

-- Set default schema
SET search_path TO public;

-- Create extensions if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Insert default admin user if not exists (password: admin123)
-- This will be created by the Spring Boot application on first run
-- The application handles user creation and role assignment

-- Log initialization
INSERT INTO public.system_logs (log_level, message, timestamp) 
VALUES ('INFO', 'Database initialized successfully', NOW())
ON CONFLICT DO NOTHING;

-- <PERSON>reate system_logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.system_logs (
    id SERIAL PRIMARY KEY,
    log_level VARCHAR(10) NOT NULL,
    message TEXT NOT NULL,
    timestamp TIMESTAMP DEFAULT NOW()
);

-- Ensure proper permissions
ALTER TABLE public.system_logs OWNER TO postgres;
