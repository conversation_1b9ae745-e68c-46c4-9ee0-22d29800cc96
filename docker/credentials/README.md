# Credentials Directory

This directory should contain the Google service account credentials files needed for the application.

## Required Files:

1. **ops-excellence-a969197613f8.json** - Google Sheets service account credentials
2. **credentials.json** - Google OAuth credentials (if needed)

## Setup Instructions:

1. Copy your Google service account JSON file to this directory
2. Rename it to `ops-excellence-a969197613f8.json`
3. Ensure the file has proper permissions (readable by the application)

## Security Note:

- These files contain sensitive credentials
- Never commit them to version control
- Use environment variables or secure secret management in production
- The Docker container will mount this directory as read-only

## File Structure:
```
docker/credentials/
├── README.md
├── ops-excellence-a969197613f8.json  (your service account file)
└── credentials.json                   (optional OAuth file)
```

## For Production:

In production environments, consider using:
- Google Cloud Secret Manager
- Kubernetes Secrets
- Docker Secrets
- Environment variables for sensitive data
