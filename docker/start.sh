#!/bin/bash

# TeamSphere Docker Startup Script

set -e

echo "Starting TeamSphere Application..."

# Create necessary directories
mkdir -p /app/logs
mkdir -p /app/uploads/profile-pics
mkdir -p /app/database_backups

# Set proper permissions
chmod -R 755 /app/uploads
chmod -R 755 /app/logs
chmod -R 755 /app/database_backups

# Wait for PostgreSQL to be ready
echo "Waiting for PostgreSQL to be ready..."
until pg_isready -h postgres -p 5432 -U postgres; do
  echo "PostgreSQL is unavailable - sleeping"
  sleep 2
done
echo "PostgreSQL is ready!"

# Start nginx in the background
echo "Starting Nginx..."
nginx -g "daemon off;" &
NGINX_PID=$!

# Function to handle shutdown
shutdown() {
    echo "Shutting down..."
    kill $NGINX_PID 2>/dev/null || true
    kill $SPRING_PID 2>/dev/null || true
    exit 0
}

# Trap signals
trap shutdown SIGTERM SIGINT

# Start Spring Boot application
echo "Starting Spring Boot application..."
java -jar \
    -Dspring.config.location=file:/app/application.properties \
    -Dspring.profiles.active=docker \
    -Djava.security.egd=file:/dev/./urandom \
    -Duser.timezone=UTC \
    ${JAVA_OPTS:-"-Xmx1g -Xms512m"} \
    /app/app.jar &

SPRING_PID=$!

# Wait for Spring Boot to start
echo "Waiting for Spring Boot to start..."
sleep 30

# Health check loop
while true; do
    if ! kill -0 $NGINX_PID 2>/dev/null; then
        echo "Nginx died, restarting..."
        nginx -g "daemon off;" &
        NGINX_PID=$!
    fi
    
    if ! kill -0 $SPRING_PID 2>/dev/null; then
        echo "Spring Boot died, exiting..."
        exit 1
    fi
    
    sleep 30
done
