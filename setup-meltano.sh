#!/bin/bash

set -e

echo "=== Meltano PostgreSQL to BigQuery Sync Setup ==="

# Step 0: Check Python version
PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
REQUIRED_VERSION="3.8"

if [[ "$(printf '%s\n' "$REQUIRED_VERSION" "$PYTHON_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]]; then
  echo "❌ Python 3.8+ is required. Found: $PYTHON_VERSION"
  echo "Please upgrade Python and try again."
  exit 1
fi

echo "✅ Python version $PYTHON_VERSION is OK"

# Step 1: Ensure python3-venv and pip are available
echo "Checking for python3-venv and pip..."
if ! dpkg -s python3-venv &>/dev/null; then
  echo "Installing python3-venv..."
  sudo apt-get update
  sudo apt-get install -y python3-venv
fi
if ! command -v pip &>/dev/null; then
  echo "Installing pip..."
  sudo apt-get install -y python3-pip
fi

# Step 2: Create virtual environment
echo "Creating virtual environment at ~/.meltano-venv ..."
python3 -m venv ~/.meltano-venv
source ~/.meltano-venv/bin/activate

# Step 3: Install Meltano
echo "Installing Meltano..."
pip install --upgrade pip
pip install meltano

# Step 4: Init Meltano project
PROJECT_NAME="teamsphere_sync"
meltano init "$PROJECT_NAME"
cd "$PROJECT_NAME"

# Step 5: Add Extractor and Loader
meltano add extractor tap-postgres
meltano add loader target-bigquery

# Step 6: Configure PostgreSQL source
echo "Configuring PostgreSQL source..."
meltano config tap-postgres set host "localhost"
meltano config tap-postgres set port "5432"
meltano config tap-postgres set user "postgres"
meltano config tap-postgres set password "voyage"
meltano config tap-postgres set database "vbs_allocation_caps"

# Step 7: Configure BigQuery Target
echo "Configuring BigQuery target..."
BQ_PROJECT_ID="ops-excellence"
BQ_DATASET_ID="ops_excellence_pg_mirror"
BQ_LOCATION="US"
BQ_KEY_PATH="/home/<USER>/ops-excellence-8e03eb84e03b.json"

meltano config target-bigquery set project "$BQ_PROJECT_ID"
meltano config target-bigquery set dataset "$BQ_DATASET_ID"
meltano config target-bigquery set location "$BQ_LOCATION"
meltano config target-bigquery set credentials_path "$BQ_KEY_PATH"
meltano config target-bigquery set flattening_enabled true
meltano config target-bigquery set flattening_max_depth 2
meltano config target-bigquery set loading_method REPLACE

# Step 8: Install plugins
meltano install

# Step 9: Select only `public.time_entries` and `public.users`
echo "Selecting only time_entries and users tables..."
meltano select tap-postgres --remove "*" || true
meltano select tap-postgres "public-time_entries" "*"
meltano select tap-postgres "public-users" "*"

# Step 10: Optional: Incremental sync (disabled)
echo ""
echo "Would you like to configure INCREMENTAL sync for known tables? (y/n)"
read -r INCREMENTAL_CONFIRM

if [[ "$INCREMENTAL_CONFIRM" == "y" ]]; then
  declare -A INCREMENTAL_TABLES=(
    ["public-time_entries"]="entry_date"
  )
  echo "Skipping incremental sync setup. All tables will use FULL_TABLE sync."
fi

# Step 11: Run initial Meltano sync
echo "Running Meltano sync with full refresh..."
meltano elt tap-postgres target-bigquery --full-refresh

# Step 12: Setup cron job for hourly sync
echo "Setting up cron job for hourly sync..."
PROJECT_PATH=$(pwd)
SYNC_SCRIPT="$PROJECT_PATH/sync_meltano.sh"

cat <<EOF > "$SYNC_SCRIPT"
#!/bin/bash
source ~/.meltano-venv/bin/activate
cd "$PROJECT_PATH" || exit
export GOOGLE_APPLICATION_CREDENTIALS="$BQ_KEY_PATH"

echo "Running Meltano sync..."
meltano elt tap-postgres target-bigquery --full-refresh >> meltano_cron.log 2>&1
EOF

chmod +x "$SYNC_SCRIPT"
#(crontab -l 2>/dev/null; echo "0 * * * * bash $SYNC_SCRIPT") | crontab -
(crontab -l 2>/dev/null; echo "*/5 * * * * bash $SYNC_SCRIPT") | crontab -


echo ""
echo "✅ Meltano sync setup complete and hourly cron job added!"
echo "Project directory: $PROJECT_PATH"

