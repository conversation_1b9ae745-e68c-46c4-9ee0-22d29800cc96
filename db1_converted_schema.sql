--
-- PostgreSQL database dump
--

-- Dumped from database version 16.9 (Ubuntu 16.9-0ubuntu0.24.04.1)
-- Dumped by pg_dump version 16.9 (Ubuntu 16.9-0ubuntu0.24.04.1)

-- Started on 2025-07-11 12:38:55 IST

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- TOC entry 5 (class 2615 OID 139511)
-- Name: public; Type: SCHEMA; Schema: -; Owner: -
-- Data Pos: 0
--

-- *not* creating schema, since initdb creates it


--
-- TOC entry 3795 (class 0 OID 0)
-- Dependencies: 5
-- Name: SCHEMA public; Type: COMMENT; Schema: -; Owner: -
-- Data Pos: 0
--

COMMENT ON SCHEMA public IS '';


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- TOC entry 241 (class 1259 OID 173198)
-- Dependencies: 5
-- Name: attendance_entries; Type: TABLE; Schema: public; Owner: -
-- Data Pos: 0
--

CREATE TABLE public.attendance_entries (
    id character varying(255) NOT NULL,
    entry_date date,
    entry_timestamp timestamp(6) without time zone,
    is_defaulter boolean,
    is_outside_office boolean,
    late_login_reason character varying(255),
    employee_id bigint NOT NULL
);


--
-- TOC entry 215 (class 1259 OID 139588)
-- Dependencies: 5
-- Name: dropdown_options; Type: TABLE; Schema: public; Owner: -
-- Data Pos: 0
--

CREATE TABLE public.dropdown_options (
    id bigint NOT NULL,
    active boolean NOT NULL,
    created_at timestamp(6) without time zone,
    created_by character varying(255),
    type character varying(255) NOT NULL,
    updated_at timestamp(6) without time zone,
    updated_by character varying(255),
    value character varying(255) NOT NULL,
    CONSTRAINT dropdown_options_type_check CHECK (((type)::text = ANY (ARRAY[('TEAM'::character varying)::text, ('LEVEL'::character varying)::text, ('PROCESS'::character varying)::text, ('PSE_PROGRAM'::character varying)::text, ('LANGUAGE'::character varying)::text, ('SHIFT'::character varying)::text])))
);


--
-- TOC entry 216 (class 1259 OID 139594)
-- Dependencies: 5 215
-- Name: dropdown_options_id_seq; Type: SEQUENCE; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE public.dropdown_options ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.dropdown_options_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- TOC entry 217 (class 1259 OID 148480)
-- Dependencies: 5
-- Name: employee; Type: TABLE; Schema: public; Owner: -
-- Data Pos: 0
--

CREATE TABLE public.employee (
    id bigint NOT NULL,
    backfill_ldap character varying(255),
    billing_start_date character varying(255),
    email character varying(255),
    first_name character varying(255),
    inactive_reason character varying(255),
    is_deleted boolean,
    language character varying(255),
    last_billing_date character varying(255),
    last_name character varying(255),
    ldap character varying(255) NOT NULL,
    lead character varying(255),
    level character varying(255),
    level_after_change character varying(255),
    level_before_change character varying(255),
    lwd_ml_start_date character varying(255),
    new_level character varying(255),
    parent bigint,
    pnse_program character varying(255),
    process character varying(255),
    profile_pic oid,
    program_manager character varying(255),
    role_change_effective_date character varying(255),
    start_date character varying(255),
    status character varying(255),
    team character varying(255),
    tenure_till_date character varying(255),
    vendor character varying(255),
    location character varying(255),
    resignation_date character varying(255),
    shift character varying(255),
    inactive boolean
);


--
-- TOC entry 218 (class 1259 OID 148485)
-- Dependencies: 5 217
-- Name: employee_id_seq; Type: SEQUENCE; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE public.employee ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.employee_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- TOC entry 219 (class 1259 OID 148486)
-- Dependencies: 5
-- Name: inactive_employees; Type: TABLE; Schema: public; Owner: -
-- Data Pos: 0
--

CREATE TABLE public.inactive_employees (
    id bigint NOT NULL,
    backfill_ldap character varying(255),
    billing_start_date character varying(255),
    deleted_at date,
    email character varying(255),
    first_name character varying(255),
    inactive_reason character varying(255),
    language character varying(255),
    last_billing_date character varying(255),
    last_name character varying(255),
    ldap character varying(255),
    lead character varying(255),
    level character varying(255),
    level_after_change character varying(255),
    level_before_change character varying(255),
    lwd_ml_start_date character varying(255),
    new_level character varying(255),
    parent bigint,
    pnse_program character varying(255),
    process character varying(255),
    profile_pic bytea,
    program_manager character varying(255),
    role_change_effective_date character varying(255),
    start_date character varying(255),
    status character varying(255),
    team character varying(255),
    tenure_till_date character varying(255),
    vendor character varying(255),
    location character varying(255),
    resignation_date character varying(255),
    shift character varying(255),
    inactive boolean
);


--
-- TOC entry 220 (class 1259 OID 148491)
-- Dependencies: 219 5
-- Name: inactive_employees_id_seq; Type: SEQUENCE; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE public.inactive_employees ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.inactive_employees_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- TOC entry 221 (class 1259 OID 148492)
-- Dependencies: 5
-- Name: leads_request; Type: TABLE; Schema: public; Owner: -
-- Data Pos: 0
--

CREATE TABLE public.leads_request (
    id bigint NOT NULL,
    employee_data text,
    is_sign_up boolean DEFAULT false NOT NULL,
    ldap character varying(255),
    request_type character varying(255),
    requested_at timestamp(6) without time zone,
    requested_by character varying(255),
    status character varying(255),
    employee_data_key character varying(255)
);


--
-- TOC entry 240 (class 1259 OID 148622)
-- Dependencies: 5
-- Name: leads_request_details; Type: TABLE; Schema: public; Owner: -
-- Data Pos: 0
--

CREATE TABLE public.leads_request_details (
    id bigint NOT NULL,
    change_type character varying(255) NOT NULL,
    created_at timestamp(6) without time zone,
    field_name character varying(255) NOT NULL,
    metadata text,
    new_value text,
    old_value text,
    target_ldap character varying(255) NOT NULL,
    leads_request_id bigint NOT NULL
);


--
-- TOC entry 239 (class 1259 OID 148621)
-- Dependencies: 5 240
-- Name: leads_request_details_id_seq; Type: SEQUENCE; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE public.leads_request_details ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.leads_request_details_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- TOC entry 222 (class 1259 OID 148498)
-- Dependencies: 221 5
-- Name: leads_request_id_seq; Type: SEQUENCE; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE public.leads_request ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.leads_request_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- TOC entry 243 (class 1259 OID 173206)
-- Dependencies: 5
-- Name: leave_balances; Type: TABLE; Schema: public; Owner: -
-- Data Pos: 0
--

CREATE TABLE public.leave_balances (
    id bigint NOT NULL,
    balance double precision,
    leave_type character varying(255),
    month character varying(255),
    employee_id bigint,
    source character varying(255),
    uploaded_at timestamp(6) without time zone,
    uploaded_by character varying(255),
    year integer
);


--
-- TOC entry 242 (class 1259 OID 173205)
-- Dependencies: 5 243
-- Name: leave_balances_id_seq; Type: SEQUENCE; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE public.leave_balances ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.leave_balances_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- TOC entry 245 (class 1259 OID 173214)
-- Dependencies: 5
-- Name: leave_usage_logs; Type: TABLE; Schema: public; Owner: -
-- Data Pos: 0
--

CREATE TABLE public.leave_usage_logs (
    id bigint NOT NULL,
    days_taken double precision,
    leave_date date,
    leave_type character varying(255),
    quarter character varying(255),
    year integer,
    employee_id bigint
);


--
-- TOC entry 244 (class 1259 OID 173213)
-- Dependencies: 5 245
-- Name: leave_usage_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE public.leave_usage_logs ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.leave_usage_logs_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- TOC entry 223 (class 1259 OID 148499)
-- Dependencies: 5
-- Name: password_reset_otps; Type: TABLE; Schema: public; Owner: -
-- Data Pos: 0
--

CREATE TABLE public.password_reset_otps (
    id bigint NOT NULL,
    expiry_time timestamp(6) without time zone NOT NULL,
    otp_code character varying(255) NOT NULL,
    used boolean NOT NULL,
    username character varying(255) NOT NULL
);


--
-- TOC entry 224 (class 1259 OID 148504)
-- Dependencies: 5 223
-- Name: password_reset_otps_id_seq; Type: SEQUENCE; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE public.password_reset_otps ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.password_reset_otps_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- TOC entry 225 (class 1259 OID 148505)
-- Dependencies: 5
-- Name: projects; Type: TABLE; Schema: public; Owner: -
-- Data Pos: 0
--

CREATE TABLE public.projects (
    id bigint NOT NULL,
    project_code character varying(255) NOT NULL,
    project_name character varying(255) NOT NULL,
    created_by bigint NOT NULL
);


--
-- TOC entry 226 (class 1259 OID 148510)
-- Dependencies: 5
-- Name: projects_backup; Type: TABLE; Schema: public; Owner: -
-- Data Pos: 0
--

CREATE TABLE public.projects_backup (
    id bigint,
    project_code character varying(255),
    project_name character varying(255),
    created_by bigint
);


--
-- TOC entry 227 (class 1259 OID 148515)
-- Dependencies: 5 225
-- Name: projects_id_seq; Type: SEQUENCE; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE public.projects ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.projects_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- TOC entry 249 (class 1259 OID 189583)
-- Dependencies: 5
-- Name: shift_details; Type: TABLE; Schema: public; Owner: -
-- Data Pos: 0
--

CREATE TABLE public.shift_details (
    id bigint NOT NULL,
    break_time character varying(255),
    code character varying(255) NOT NULL,
    end_time character varying(255),
    max_login_time character varying(255),
    start_time character varying(255),
    name character varying(255),
    created_at timestamp(6) without time zone,
    grace_period_minutes integer,
    half_time character varying(255),
    updated_at timestamp(6) without time zone
);


--
-- TOC entry 248 (class 1259 OID 189582)
-- Dependencies: 5 249
-- Name: shift_details_id_seq; Type: SEQUENCE; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE public.shift_details ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.shift_details_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- TOC entry 228 (class 1259 OID 148516)
-- Dependencies: 5
-- Name: time_entries; Type: TABLE; Schema: public; Owner: -
-- Data Pos: 0
--

CREATE TABLE public.time_entries (
    id bigint NOT NULL,
    activity character varying(255) NOT NULL,
    comment character varying(255),
    entry_date date NOT NULL,
    ldap character varying(255) NOT NULL,
    process character varying(255) NOT NULL,
    rejection_comment character varying(255),
    status character varying(255) NOT NULL,
    time_in_mins integer NOT NULL,
    lead_id bigint,
    project_id bigint NOT NULL,
    user_id bigint NOT NULL,
    attendance_type character varying(255),
    is_overtime boolean NOT NULL,
    CONSTRAINT time_entries_activity_check CHECK (((activity)::text = ANY (ARRAY[('ABSENTEEISM'::character varying)::text, ('TRAINING'::character varying)::text, ('PRODUCTION'::character varying)::text, ('MEETING'::character varying)::text, ('EVENTS'::character varying)::text, ('DOWNTIME'::character varying)::text, ('COVID_SYSTEM_ISSUE'::character varying)::text, ('NO_VOLUME'::character varying)::text, ('OTHER_PROJECTS'::character varying)::text, ('HOLIDAY'::character varying)::text, ('COMPOFF'::character varying)::text]))),
    CONSTRAINT time_entries_status_check CHECK (((status)::text = ANY (ARRAY[('PENDING'::character varying)::text, ('APPROVED'::character varying)::text, ('REJECTED'::character varying)::text])))
);


--
-- TOC entry 229 (class 1259 OID 148523)
-- Dependencies: 228 5
-- Name: time_entries_id_seq; Type: SEQUENCE; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE public.time_entries ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.time_entries_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- TOC entry 230 (class 1259 OID 148524)
-- Dependencies: 5
-- Name: time_sheet; Type: TABLE; Schema: public; Owner: -
-- Data Pos: 0
--

CREATE TABLE public.time_sheet (
    id bigint NOT NULL,
    activity character varying(255),
    billing_code character varying(255),
    comment character varying(255),
    date character varying(255),
    ldap character varying(255),
    lead_ldap character varying(255),
    masked_orgid character varying(255),
    minutes character varying(255),
    process character varying(255),
    project character varying(255),
    role character varying(255),
    status character varying(255),
    subrole character varying(255),
    team character varying(255),
    vendor character varying(255)
);


--
-- TOC entry 231 (class 1259 OID 148529)
-- Dependencies: 230 5
-- Name: time_sheet_id_seq; Type: SEQUENCE; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE public.time_sheet ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.time_sheet_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- TOC entry 232 (class 1259 OID 148530)
-- Dependencies: 5
-- Name: user_edit_logs; Type: TABLE; Schema: public; Owner: -
-- Data Pos: 0
--

CREATE TABLE public.user_edit_logs (
    id bigint NOT NULL,
    changed_at timestamp(6) without time zone,
    changed_by character varying(255) NOT NULL,
    field_name character varying(255) NOT NULL,
    new_value text,
    old_value text,
    user_ldap character varying(255) NOT NULL
);


--
-- TOC entry 233 (class 1259 OID 148535)
-- Dependencies: 5 232
-- Name: user_edit_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE public.user_edit_logs ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.user_edit_logs_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- TOC entry 234 (class 1259 OID 148536)
-- Dependencies: 5
-- Name: user_project_mapping; Type: TABLE; Schema: public; Owner: -
-- Data Pos: 0
--

CREATE TABLE public.user_project_mapping (
    id bigint NOT NULL,
    assigned_date date,
    status character varying(255),
    project_id bigint NOT NULL,
    user_id bigint NOT NULL
);


--
-- TOC entry 235 (class 1259 OID 148539)
-- Dependencies: 5 234
-- Name: user_project_mapping_id_seq; Type: SEQUENCE; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE public.user_project_mapping ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.user_project_mapping_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- TOC entry 236 (class 1259 OID 148540)
-- Dependencies: 5
-- Name: users; Type: TABLE; Schema: public; Owner: -
-- Data Pos: 0
--

CREATE TABLE public.users (
    id bigint NOT NULL,
    password character varying(255),
    password_change_required boolean,
    role character varying(255),
    username character varying(255) NOT NULL,
    email character varying(255),
    google_id character varying(255),
    auth_provider character varying(255) DEFAULT 'LOCAL'::character varying NOT NULL,
    first_name character varying(255),
    last_name character varying(255),
    CONSTRAINT chk_auth_provider CHECK (((auth_provider)::text = ANY (ARRAY[('LOCAL'::character varying)::text, ('GOOGLE'::character varying)::text]))),
    CONSTRAINT users_role_check CHECK (((role)::text = ANY (ARRAY[('USER'::character varying)::text, ('LEAD'::character varying)::text, ('MANAGER'::character varying)::text, ('ADMIN_OPS_MANAGER'::character varying)::text, ('ACCOUNT_MANAGER'::character varying)::text])))
);


--
-- TOC entry 237 (class 1259 OID 148546)
-- Dependencies: 5 236
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE public.users ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.users_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- TOC entry 238 (class 1259 OID 148547)
-- Dependencies: 5
-- Name: vbs_who; Type: TABLE; Schema: public; Owner: -
-- Data Pos: 0
--

CREATE TABLE public.vbs_who (
    ldap character varying(255) NOT NULL,
    email_address character varying(255),
    locales character varying(255),
    location character varying(255),
    name character varying(255),
    primary_language character varying(255),
    primary_program_alignment character varying(255),
    role character varying(255),
    secondary_language character varying(255),
    start_date timestamp(6) without time zone,
    status character varying(255),
    teams character varying(255),
    tenure character varying(255),
    tenure_months character varying(255),
    lead character varying(255)
);


--
-- TOC entry 251 (class 1259 OID 197775)
-- Dependencies: 5
-- Name: vunno_audit_log; Type: TABLE; Schema: public; Owner: -
-- Data Pos: 0
--

CREATE TABLE public.vunno_audit_log (
    id bigint NOT NULL,
    action_type character varying(20) NOT NULL,
    change_description text,
    change_reason text,
    changed_at timestamp(6) without time zone,
    changed_by character varying(255) NOT NULL,
    changed_by_role character varying(100),
    new_status character varying(255),
    new_values text,
    previous_status character varying(255),
    previous_values text,
    vunno_response_id bigint NOT NULL
);


--
-- TOC entry 250 (class 1259 OID 197774)
-- Dependencies: 251 5
-- Name: vunno_audit_log_id_seq; Type: SEQUENCE; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE public.vunno_audit_log ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.vunno_audit_log_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- TOC entry 247 (class 1259 OID 173222)
-- Dependencies: 5
-- Name: vunno_responses; Type: TABLE; Schema: public; Owner: -
-- Data Pos: 0
--

CREATE TABLE public.vunno_responses (
    id bigint NOT NULL,
    application_type character varying(255),
    approver character varying(255),
    backup character varying(255),
    duration character varying(255),
    from_date date,
    leave_type character varying(255),
    org_screenshot character varying(2048),
    program character varying(255),
    requestor_name character varying(255),
    status character varying(255),
    team character varying(255),
    timesheet_screenshot character varying(2048),
    "timestamp" timestamp(6) without time zone,
    to_date date,
    employee_id bigint NOT NULL,
    backup_ldap character varying(255),
    backup_name character varying(255),
    deleted_at timestamp(6) without time zone
);


--
-- TOC entry 246 (class 1259 OID 173221)
-- Dependencies: 247 5
-- Name: vunno_responses_id_seq; Type: SEQUENCE; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE public.vunno_responses ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.vunno_responses_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- TOC entry 3423 (class 2606 OID 173204)
-- Dependencies: 241
-- Name: attendance_entries attendance_entries_pkey; Type: CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.attendance_entries
    ADD CONSTRAINT attendance_entries_pkey PRIMARY KEY (id);


--
-- TOC entry 3384 (class 2606 OID 139664)
-- Dependencies: 215
-- Name: dropdown_options dropdown_options_pkey; Type: CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.dropdown_options
    ADD CONSTRAINT dropdown_options_pkey PRIMARY KEY (id);


--
-- TOC entry 3386 (class 2606 OID 148553)
-- Dependencies: 217
-- Name: employee employee_pkey; Type: CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.employee
    ADD CONSTRAINT employee_pkey PRIMARY KEY (id);


--
-- TOC entry 3390 (class 2606 OID 148555)
-- Dependencies: 219
-- Name: inactive_employees inactive_employees_pkey; Type: CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.inactive_employees
    ADD CONSTRAINT inactive_employees_pkey PRIMARY KEY (id);


--
-- TOC entry 3421 (class 2606 OID 148628)
-- Dependencies: 240
-- Name: leads_request_details leads_request_details_pkey; Type: CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.leads_request_details
    ADD CONSTRAINT leads_request_details_pkey PRIMARY KEY (id);


--
-- TOC entry 3392 (class 2606 OID 148557)
-- Dependencies: 221
-- Name: leads_request leads_request_pkey; Type: CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.leads_request
    ADD CONSTRAINT leads_request_pkey PRIMARY KEY (id);


--
-- TOC entry 3425 (class 2606 OID 173212)
-- Dependencies: 243
-- Name: leave_balances leave_balances_pkey; Type: CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.leave_balances
    ADD CONSTRAINT leave_balances_pkey PRIMARY KEY (id);


--
-- TOC entry 3429 (class 2606 OID 173220)
-- Dependencies: 245
-- Name: leave_usage_logs leave_usage_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.leave_usage_logs
    ADD CONSTRAINT leave_usage_logs_pkey PRIMARY KEY (id);


--
-- TOC entry 3394 (class 2606 OID 148559)
-- Dependencies: 223
-- Name: password_reset_otps password_reset_otps_pkey; Type: CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.password_reset_otps
    ADD CONSTRAINT password_reset_otps_pkey PRIMARY KEY (id);


--
-- TOC entry 3396 (class 2606 OID 148561)
-- Dependencies: 225
-- Name: projects projects_pkey; Type: CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.projects
    ADD CONSTRAINT projects_pkey PRIMARY KEY (id);


--
-- TOC entry 3433 (class 2606 OID 189589)
-- Dependencies: 249
-- Name: shift_details shift_details_pkey; Type: CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.shift_details
    ADD CONSTRAINT shift_details_pkey PRIMARY KEY (id);


--
-- TOC entry 3400 (class 2606 OID 148563)
-- Dependencies: 228
-- Name: time_entries time_entries_pkey; Type: CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.time_entries
    ADD CONSTRAINT time_entries_pkey PRIMARY KEY (id);


--
-- TOC entry 3402 (class 2606 OID 148565)
-- Dependencies: 230
-- Name: time_sheet time_sheet_pkey; Type: CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.time_sheet
    ADD CONSTRAINT time_sheet_pkey PRIMARY KEY (id);


--
-- TOC entry 3398 (class 2606 OID 148567)
-- Dependencies: 225
-- Name: projects uk1batb7mq0elcfcs3d6maqo6sg; Type: CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.projects
    ADD CONSTRAINT uk1batb7mq0elcfcs3d6maqo6sg UNIQUE (project_code);


--
-- TOC entry 3427 (class 2606 OID 173230)
-- Dependencies: 243 243 243
-- Name: leave_balances uk2gaelbhuo3jlp1msqmhujdmr2; Type: CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.leave_balances
    ADD CONSTRAINT uk2gaelbhuo3jlp1msqmhujdmr2 UNIQUE (employee_id, month, leave_type);


--
-- TOC entry 3435 (class 2606 OID 189591)
-- Dependencies: 249
-- Name: shift_details uk8nibmvxhtgp6jqa9tqa18rv0j; Type: CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.shift_details
    ADD CONSTRAINT uk8nibmvxhtgp6jqa9tqa18rv0j UNIQUE (code);


--
-- TOC entry 3411 (class 2606 OID 148569)
-- Dependencies: 236
-- Name: users ukr43af9ap4edm43mmtq01oddj6; Type: CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT ukr43af9ap4edm43mmtq01oddj6 UNIQUE (username);


--
-- TOC entry 3388 (class 2606 OID 148571)
-- Dependencies: 217
-- Name: employee ukrghs6qdg65k92vitg3aajsrpf; Type: CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.employee
    ADD CONSTRAINT ukrghs6qdg65k92vitg3aajsrpf UNIQUE (ldap);


--
-- TOC entry 3404 (class 2606 OID 148573)
-- Dependencies: 232
-- Name: user_edit_logs user_edit_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.user_edit_logs
    ADD CONSTRAINT user_edit_logs_pkey PRIMARY KEY (id);


--
-- TOC entry 3406 (class 2606 OID 148575)
-- Dependencies: 234
-- Name: user_project_mapping user_project_mapping_pkey; Type: CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.user_project_mapping
    ADD CONSTRAINT user_project_mapping_pkey PRIMARY KEY (id);


--
-- TOC entry 3413 (class 2606 OID 156816)
-- Dependencies: 236
-- Name: users users_email_key; Type: CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_key UNIQUE (email);


--
-- TOC entry 3415 (class 2606 OID 156818)
-- Dependencies: 236
-- Name: users users_google_id_key; Type: CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_google_id_key UNIQUE (google_id);


--
-- TOC entry 3417 (class 2606 OID 148577)
-- Dependencies: 236
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- TOC entry 3419 (class 2606 OID 148579)
-- Dependencies: 238
-- Name: vbs_who vbs_who_pkey; Type: CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.vbs_who
    ADD CONSTRAINT vbs_who_pkey PRIMARY KEY (ldap);


--
-- TOC entry 3437 (class 2606 OID 197781)
-- Dependencies: 251
-- Name: vunno_audit_log vunno_audit_log_pkey; Type: CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.vunno_audit_log
    ADD CONSTRAINT vunno_audit_log_pkey PRIMARY KEY (id);


--
-- TOC entry 3431 (class 2606 OID 173228)
-- Dependencies: 247
-- Name: vunno_responses vunno_responses_pkey; Type: CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.vunno_responses
    ADD CONSTRAINT vunno_responses_pkey PRIMARY KEY (id);


--
-- TOC entry 3407 (class 1259 OID 156824)
-- Dependencies: 236
-- Name: idx_users_auth_provider; Type: INDEX; Schema: public; Owner: -
-- Data Pos: 0
--

CREATE INDEX idx_users_auth_provider ON public.users USING btree (auth_provider);


--
-- TOC entry 3408 (class 1259 OID 156819)
-- Dependencies: 236
-- Name: idx_users_email; Type: INDEX; Schema: public; Owner: -
-- Data Pos: 0
--

CREATE INDEX idx_users_email ON public.users USING btree (email);


--
-- TOC entry 3409 (class 1259 OID 156820)
-- Dependencies: 236
-- Name: idx_users_google_id; Type: INDEX; Schema: public; Owner: -
-- Data Pos: 0
--

CREATE INDEX idx_users_google_id ON public.users USING btree (google_id);


--
-- TOC entry 3440 (class 2606 OID 148580)
-- Dependencies: 236 228 3417
-- Name: time_entries fk5oy4d9re28blktipmwp24smmu; Type: FK CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.time_entries
    ADD CONSTRAINT fk5oy4d9re28blktipmwp24smmu FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- TOC entry 3443 (class 2606 OID 148585)
-- Dependencies: 234 3396 225
-- Name: user_project_mapping fk69o3i1r2airn6vxmk9kna0iee; Type: FK CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.user_project_mapping
    ADD CONSTRAINT fk69o3i1r2airn6vxmk9kna0iee FOREIGN KEY (project_id) REFERENCES public.projects(id);


--
-- TOC entry 3444 (class 2606 OID 148590)
-- Dependencies: 3417 234 236
-- Name: user_project_mapping fk73rc7cd5fgve4adu8wyr6vag9; Type: FK CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.user_project_mapping
    ADD CONSTRAINT fk73rc7cd5fgve4adu8wyr6vag9 FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- TOC entry 3441 (class 2606 OID 148595)
-- Dependencies: 228 3396 225
-- Name: time_entries fk7p5j2i2xr2q8moop1x2g6wol; Type: FK CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.time_entries
    ADD CONSTRAINT fk7p5j2i2xr2q8moop1x2g6wol FOREIGN KEY (project_id) REFERENCES public.projects(id);


--
-- TOC entry 3449 (class 2606 OID 173246)
-- Dependencies: 217 3386 247
-- Name: vunno_responses fk8d9477vhkdrqxtbsh65mdw6fy; Type: FK CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.vunno_responses
    ADD CONSTRAINT fk8d9477vhkdrqxtbsh65mdw6fy FOREIGN KEY (employee_id) REFERENCES public.employee(id);


--
-- TOC entry 3446 (class 2606 OID 173231)
-- Dependencies: 241 217 3386
-- Name: attendance_entries fk9che4v38dgqfal63p8bxsph8a; Type: FK CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.attendance_entries
    ADD CONSTRAINT fk9che4v38dgqfal63p8bxsph8a FOREIGN KEY (employee_id) REFERENCES public.employee(id);


--
-- TOC entry 3448 (class 2606 OID 173241)
-- Dependencies: 217 3386 245
-- Name: leave_usage_logs fkelbfiw1oq9ahg17rplm306qdh; Type: FK CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.leave_usage_logs
    ADD CONSTRAINT fkelbfiw1oq9ahg17rplm306qdh FOREIGN KEY (employee_id) REFERENCES public.employee(id);


--
-- TOC entry 3439 (class 2606 OID 148600)
-- Dependencies: 225 3417 236
-- Name: projects fkf1ph00os6khfle3ub9b50x594; Type: FK CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.projects
    ADD CONSTRAINT fkf1ph00os6khfle3ub9b50x594 FOREIGN KEY (created_by) REFERENCES public.users(id);


--
-- TOC entry 3438 (class 2606 OID 181390)
-- Dependencies: 217 3411 236
-- Name: employee fkfl6u45jm07l4dl9j67740ohhs; Type: FK CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.employee
    ADD CONSTRAINT fkfl6u45jm07l4dl9j67740ohhs FOREIGN KEY (ldap) REFERENCES public.users(username);


--
-- TOC entry 3445 (class 2606 OID 148629)
-- Dependencies: 240 3392 221
-- Name: leads_request_details fkfn8t3he7ajdu91968u05uwf8x; Type: FK CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.leads_request_details
    ADD CONSTRAINT fkfn8t3he7ajdu91968u05uwf8x FOREIGN KEY (leads_request_id) REFERENCES public.leads_request(id);


--
-- TOC entry 3442 (class 2606 OID 148605)
-- Dependencies: 236 3417 228
-- Name: time_entries fkfnx76v9wx36b5qs32s22dwwkh; Type: FK CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.time_entries
    ADD CONSTRAINT fkfnx76v9wx36b5qs32s22dwwkh FOREIGN KEY (lead_id) REFERENCES public.users(id);


--
-- TOC entry 3447 (class 2606 OID 173236)
-- Dependencies: 3386 217 243
-- Name: leave_balances fksdudvuf1g14itmqk7yg9621h1; Type: FK CONSTRAINT; Schema: public; Owner: -
-- Data Pos: 0
--

ALTER TABLE ONLY public.leave_balances
    ADD CONSTRAINT fksdudvuf1g14itmqk7yg9621h1 FOREIGN KEY (employee_id) REFERENCES public.employee(id);


-- Completed on 2025-07-11 13:09:02 IST

--
-- PostgreSQL database dump complete
--

