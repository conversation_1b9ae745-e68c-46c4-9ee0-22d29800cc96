#!/bin/bash

# Docker Browser Isolation Script
# This script runs browsers in isolated Docker containers

set -e

echo "🐳 Setting up Docker Browser Isolation..."

# Check if Docker is installed
if ! command -v docker >/dev/null 2>&1; then
    echo "❌ Docker not found. Installing..."
    
    # Install Docker
    curl -fsSL https://get.docker.com -o get-docker.sh
    sudo sh get-docker.sh
    
    # Add user to docker group
    sudo usermod -aG docker $USER
    
    echo "✅ Docker installed. Please log out and log back in."
    echo "Then run this script again."
    exit 0
fi

# Create Dockerfile for isolated browser
cat > Dockerfile.browser << 'EOF'
FROM ubuntu:22.04

# Avoid interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Install dependencies
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    software-properties-common \
    xvfb \
    x11vnc \
    fluxbox \
    novnc \
    websockify \
    supervisor \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Chrome
RUN wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list \
    && apt-get update \
    && apt-get install -y google-chrome-stable \
    && rm -rf /var/lib/apt/lists/*

# Install Firefox
RUN add-apt-repository ppa:mozillateam/ppa -y \
    && apt-get update \
    && apt-get install -y firefox \
    && rm -rf /var/lib/apt/lists/*

# Install VS Code
RUN wget -qO- https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor > packages.microsoft.gpg \
    && install -o root -g root -m 644 packages.microsoft.gpg /etc/apt/trusted.gpg.d/ \
    && echo "deb [arch=amd64,arm64,armhf signed-by=/etc/apt/trusted.gpg.d/packages.microsoft.gpg] https://packages.microsoft.com/repos/code stable main" > /etc/apt/sources.list.d/vscode.list \
    && apt-get update \
    && apt-get install -y code \
    && rm -rf /var/lib/apt/lists/*

# Create user
RUN useradd -m -s /bin/bash user && \
    echo 'user:password' | chpasswd

# Setup VNC
RUN mkdir -p /home/<USER>/.vnc
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Expose VNC port
EXPOSE 6080

# Start supervisor
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
EOF

# Create supervisor configuration
cat > supervisord.conf << 'EOF'
[supervisord]
nodaemon=true
user=root

[program:xvfb]
command=/usr/bin/Xvfb :1 -screen 0 1920x1080x24
autorestart=true
user=user

[program:fluxbox]
command=/usr/bin/fluxbox
environment=DISPLAY=:1
autorestart=true
user=user

[program:x11vnc]
command=/usr/bin/x11vnc -display :1 -nopw -listen localhost -xkb -ncache 10 -ncache_cr -forever
autorestart=true
user=user

[program:novnc]
command=/usr/share/novnc/utils/launch.sh --vnc localhost:5900 --listen 6080
autorestart=true
user=user
EOF

# Build Docker image
echo "🔨 Building Docker image..."
docker build -f Dockerfile.browser -t isolated-browser .

# Create browser launcher functions
cat > launch-isolated-browser.sh << 'EOF'
#!/bin/bash

# Function to launch isolated browser
launch_browser() {
    local browser=$1
    local session_name="browser-session-$(date +%s)"
    
    echo "🚀 Launching isolated $browser session: $session_name"
    
    # Run container with random hostname and user agent
    docker run -d \
        --name "$session_name" \
        --hostname "host-$(openssl rand -hex 4)" \
        -p 6080:6080 \
        --rm \
        isolated-browser
    
    echo "🌐 Browser accessible at: http://localhost:6080"
    echo "📝 Session name: $session_name"
    echo ""
    echo "To stop session: docker stop $session_name"
    
    # Open browser automatically
    if command -v xdg-open >/dev/null 2>&1; then
        sleep 3
        xdg-open "http://localhost:6080"
    fi
}

# Function to clean up old sessions
cleanup_sessions() {
    echo "🧹 Cleaning up old browser sessions..."
    docker ps -a --filter "name=browser-session-" --format "{{.Names}}" | xargs -r docker rm -f
}

# Function to list active sessions
list_sessions() {
    echo "📋 Active browser sessions:"
    docker ps --filter "name=browser-session-" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
}

case "$1" in
    "chrome"|"chromium")
        launch_browser "chrome"
        ;;
    "firefox")
        launch_browser "firefox"
        ;;
    "cleanup")
        cleanup_sessions
        ;;
    "list")
        list_sessions
        ;;
    *)
        echo "Usage: $0 {chrome|firefox|cleanup|list}"
        echo ""
        echo "Examples:"
        echo "  $0 chrome    # Launch isolated Chrome session"
        echo "  $0 firefox   # Launch isolated Firefox session"
        echo "  $0 list      # List active sessions"
        echo "  $0 cleanup   # Clean up old sessions"
        exit 1
        ;;
esac
EOF

chmod +x launch-isolated-browser.sh

echo "✅ Docker browser isolation setup complete!"
echo ""
echo "🚀 Usage:"
echo "./launch-isolated-browser.sh chrome   # Launch isolated Chrome"
echo "./launch-isolated-browser.sh firefox  # Launch isolated Firefox"
echo "./launch-isolated-browser.sh list     # List active sessions"
echo "./launch-isolated-browser.sh cleanup  # Clean up sessions"

# Clean up temporary files
rm -f get-docker.sh Dockerfile.browser supervisord.conf
