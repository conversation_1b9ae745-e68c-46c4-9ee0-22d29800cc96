#!/bin/bash

# TeamSphere Docker Quick Start Script
# This script provides a quick way to get TeamSphere running with Docker

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 TeamSphere Docker Quick Start${NC}"
echo "=================================="

# Check if Docker is installed and running
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker is not installed${NC}"
    echo "Please install Docker from: https://docs.docker.com/get-docker/"
    exit 1
fi

if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker is not running${NC}"
    echo "Please start Docker and try again"
    exit 1
fi

if ! docker compose version &> /dev/null; then
    echo -e "${RED}❌ Docker Compose is not available${NC}"
    echo "Please install Docker Compose from: https://docs.docker.com/compose/install/"
    exit 1
fi

echo -e "${GREEN}✅ Docker is ready${NC}"

# Check if .env file exists
if [ ! -f .env ]; then
    echo -e "${YELLOW}⚠️  .env file not found, creating from example...${NC}"
    cp .env.example .env
    echo -e "${YELLOW}📝 Please edit .env file with your configuration${NC}"
    echo -e "${YELLOW}   Especially update DOCKER_USERNAME if you plan to push to Docker Hub${NC}"
fi

# Check if credentials exist
if [ ! -f docker/credentials/ops-excellence-a969197613f8.json ]; then
    echo -e "${YELLOW}⚠️  Google service account credentials not found${NC}"
    echo -e "${YELLOW}   Please copy your credentials file to: docker/credentials/ops-excellence-a969197613f8.json${NC}"
    echo -e "${YELLOW}   Some features may not work without proper credentials${NC}"
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Ask user what they want to do
echo ""
echo -e "${BLUE}What would you like to do?${NC}"
echo "1) Start development environment (with database admin)"
echo "2) Start production environment"
echo "3) Setup QA environment"
echo "4) Build and push to Docker Hub"
echo "5) View running containers"
echo "6) Stop all containers"
echo "7) View logs"
echo "8) Clean up (remove containers and volumes)"

read -p "Enter your choice (1-8): " choice

case $choice in
    1)
        echo -e "${GREEN}🔧 Starting development environment...${NC}"
        docker compose up -d
        echo ""
        echo -e "${GREEN}✅ Development environment started!${NC}"
        echo -e "${YELLOW}📱 Frontend: http://localhost${NC}"
        echo -e "${YELLOW}🔧 Backend API: http://localhost:8080${NC}"
        echo -e "${YELLOW}🗄️  Database Admin: http://localhost:8081${NC}"
        echo -e "${YELLOW}📊 Health Check: http://localhost:8080/actuator/health${NC}"
        ;;
    2)
        echo -e "${GREEN}🏭 Starting production environment...${NC}"
        docker compose -f docker-compose.prod.yml up -d
        echo ""
        echo -e "${GREEN}✅ Production environment started!${NC}"
        echo -e "${YELLOW}📱 Application: http://localhost${NC}"
        echo -e "${YELLOW}🔧 Backend API: http://localhost:8080${NC}"
        echo -e "${YELLOW}📊 Health Check: http://localhost:8080/actuator/health${NC}"
        ;;
    3)
        echo -e "${GREEN}🧪 Setting up QA environment...${NC}"
        if [ -f deployment-scripts/setup-qa-environment.sh ]; then
            ./deployment-scripts/setup-qa-environment.sh
        else
            echo -e "${RED}❌ QA setup script not found${NC}"
        fi
        ;;
    4)
        echo -e "${GREEN}🐳 Building and pushing to Docker Hub...${NC}"
        if [ -f deployment-scripts/build-and-push.sh ]; then
            ./deployment-scripts/build-and-push.sh
        else
            echo -e "${RED}❌ Build and push script not found${NC}"
        fi
        ;;
    5)
        echo -e "${GREEN}📋 Running containers:${NC}"
        docker ps
        echo ""
        echo -e "${GREEN}📊 Container stats:${NC}"
        docker stats --no-stream
        ;;
    6)
        echo -e "${YELLOW}🛑 Stopping all containers...${NC}"
        docker compose down 2>/dev/null || true
        docker compose -f docker-compose.prod.yml down 2>/dev/null || true
        if [ -d qa-environment ]; then
            cd qa-environment && ./stop-qa.sh 2>/dev/null || true
        fi
        echo -e "${GREEN}✅ All containers stopped${NC}"
        ;;
    7)
        echo -e "${GREEN}📜 Recent logs:${NC}"
        echo "Choose which logs to view:"
        echo "1) Development environment"
        echo "2) Production environment"
        echo "3) QA environment"
        read -p "Enter choice (1-3): " log_choice
        case $log_choice in
            1) docker compose logs --tail=50 -f ;;
            2) docker compose -f docker-compose.prod.yml logs --tail=50 -f ;;
            3)
                if [ -d qa-environment ]; then
                    cd qa-environment && ./logs-qa.sh
                else
                    echo -e "${RED}❌ QA environment not found${NC}"
                fi
                ;;
            *) echo -e "${RED}❌ Invalid choice${NC}" ;;
        esac
        ;;
    8)
        echo -e "${RED}🧹 This will remove all containers and volumes. Data will be lost!${NC}"
        read -p "Are you sure? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo -e "${YELLOW}🧹 Cleaning up...${NC}"
            docker compose down -v 2>/dev/null || true
            docker compose -f docker-compose.prod.yml down -v 2>/dev/null || true
            if [ -d qa-environment ]; then
                cd qa-environment && ./reset-qa.sh 2>/dev/null || true
            fi
            docker system prune -f
            echo -e "${GREEN}✅ Cleanup completed${NC}"
        fi
        ;;
    *)
        echo -e "${RED}❌ Invalid choice${NC}"
        exit 1
        ;;
esac

echo ""
echo -e "${GREEN}📚 For more information, see README-Docker.md${NC}"
